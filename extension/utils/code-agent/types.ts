import { McpHub } from "../../services/mcp/McpHub";

export enum CodeAgentActionEnum {
  readFile = 'readFile',
  editFile = 'editFile',
  deleteFile = 'deleteFile',
  grepSearch = 'grepSearch',
  listDir = 'listDir',
  executeCommand = 'executeCommand',
  tryToFinish = 'tryToFinish',
  useMCPTool= 'useMCPTool',
  userQuery = 'userQuery',
  default = 'default'
}

export enum EditCodeAgentActionEnum {
  default = 'default'
}

// Coding Agent 共享存储接口
export interface CodingAgentSharedStore {
  // 用户输入和上下文
  userQuery: string
  workingDir: string
  
  // 主决策代理状态
  currentAction: ActionRecord | null
  history: History[]
  editOperations: EditOperation[]
  
  output: Output
  mcpHub?: McpHub
}


export interface History {
  role: 'user' | 'assistant',
  content: Record<string, any> | string
}

export interface ActionRecord {
  tool: string // 使用的工具 (例如 readFile)
  params: Record<string, any>
  result: any
  success: boolean
  timestamp: number
}

export interface EditOperation {
  startLine: number
  endLine: number
  replacement: string
}

// 节点执行结果接口
export interface NodeResult {
  nextAction: string
  data?: any
}

// 工具参数接口
export interface ReadFileParams {
  targetFile: string
  explanation?: string
}

export interface EditFileParams {
  targetFile: string
  instructions: string
  codeEdit: string
}

export interface DeleteFileParams {
  targetFile: string
  explanation?: string
}

export interface GrepSearchParams {
  query: string
  caseSensitive?: boolean
  includePattern?: string
  excludePattern?: string
  explanation?: string
}

export interface ListDirParams {
  relativeWorkspacePath: string
  explanation?: string
}

export interface ExecuteCommandParams {
  command: string
  requiresApproval: boolean
  explanation?: string
}

// 搜索结果接口
export interface SearchResult {
  file: string
  line_number: number
  content: string
}


export interface Output {
  text: (text: string) => void
  markdown?: () => void
  confirm?: (options: any[]) => void
  stream?: boolean
}