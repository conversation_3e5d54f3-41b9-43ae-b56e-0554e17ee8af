import React, { useEffect, useState } from "react";
import {
    Text,
    Theme,
    Card,
    Flex,
    Heading,
    Box,
    Separator,
} from "@radix-ui/themes";

declare global {
    interface Window {
        webviewInfo?: {
            viewType: string;
            title: string;
            panelId: string;
        };
        vscode: any;
    }
}

// 模拟用户数据
const mockUserData = {
    id: "USER_123456",
    registerTime: "2024-03-15",
    usage: {
        used: 75,
        total: 100,
        unit: "次",
        expireDate: "2024-12-31",
    },
};
const mockLogs = [
    {
        time: "2024-03-20 14:30:25",
        action: "使用 AI 代码生成",
        status: "成功",
        details: "生成了 React 组件代码",
    },
    {
        time: "2024-03-20 13:15:10",
        action: "代码优化建议",
        status: "成功",
        details: "优化了性能问题",
    },
    {
        time: "2024-03-19 16:45:33",
        action: "代码审查",
        status: "成功",
        details: "完成了代码审查任务",
    },
    {
        time: "2024-03-19 11:20:15",
        action: "使用 AI 代码生成",
        status: "成功",
        details: "生成了 API 接口代码",
    },
    {
        time: "2024-03-18 09:05:42",
        action: "代码重构",
        status: "成功",
        details: "重构了用户认证模块",
    },
];

function App() {
    useEffect(() => {
        console.log("App component mounted");

        // 通知 VSCode webview 获取数据
        const vscode = (window as any).vscode;
        if (vscode) {
            // vscode.postMessage({ type: "getContent" });
        } else {
            console.error("VSCode API not found");
        }

        // 监听来自 VSCode 的消息
        const messageHandler = (event: MessageEvent) => {
            const message = event.data;
            console.log("App received message:", message);
        };

        window.addEventListener("message", messageHandler);

        return () => {
            window.removeEventListener("message", messageHandler);
        };
    }, []);

    const [userData, setUserData] = useState(mockUserData);
    const [logs, setLogs] = useState(mockLogs);

    return (
        <Theme className="w-full h-full" appearance="dark">
            <Box className="p-6">
                {/* Header 部分 */}
                <Card className="mb-6">
                    <Flex direction="column" gap="3">
                        <Heading size="5">用户信息</Heading>
                        <Flex gap="4">
                            <Text>用户ID：{userData.id}</Text>
                            <Text>注册时间：{userData.registerTime}</Text>
                        </Flex>
                    </Flex>
                </Card>

                {/* 套餐使用情况 */}
                <Card>
                    <Flex direction="column" gap="3">
                        <Heading size="5">套餐使用情况</Heading>
                        <Flex direction="column" gap="2">
                            <Text>
                                已使用：{userData.usage.used}
                                {userData.usage.unit}
                            </Text>
                            <Text>
                                剩余额度：
                                {userData.usage.total - userData.usage.used}
                                {userData.usage.unit}
                            </Text>
                            <Text>有效期至：{userData.usage.expireDate}</Text>
                        </Flex>
                    </Flex>
                </Card>

                <Card className="mt-6">
                    <Flex direction="column" gap="3">
                        <Heading size="5">最近操作日志</Heading>
                        <Flex direction="column" gap="2">
                            {logs.map((log, index) => (
                                <React.Fragment key={index}>
                                    <Flex direction="column" gap="1">
                                        <Flex justify="between" align="center">
                                            <Text size="2" color="gray">
                                                {log.time}
                                            </Text>
                                            <Text size="2" color="green">
                                                {log.status}
                                            </Text>
                                        </Flex>
                                        <Text>{log.action}</Text>
                                        <Text size="2" color="gray">
                                            {log.details}
                                        </Text>
                                    </Flex>
                                    {index < logs.length - 1 && (
                                        <Separator className="my-2" />
                                    )}
                                </React.Fragment>
                            ))}
                        </Flex>
                    </Flex>
                </Card>
            </Box>
        </Theme>
    );
}

export default App;
