export function genCodeEditPrompt(
  fileContent: string,
  instructions: string,
  codeEdit: string
): string {
  return (
    `
  As a code editing assistant, I need to convert the following code edit instruction 
  and code edit pattern into specific edit operations (startLine, endLine, replacement).
  
  FILE CONTENT:
  ${fileContent}
  
  EDIT INSTRUCTIONS: 
  ${instructions}
  
  CODE EDIT PATTERN (markers like "// ... existing code ..." indicate unchanged code):
  ${codeEdit}
  
  Analyze the file content and the edit pattern to determine exactly where changes should be made. 
  Be very careful with start and end lines. They are 1-indexed and inclusive. These will be REPLACED, not APPENDED!
  If you want APPEND, just copy that line as the first line of the replacement.
  Return a JSON object with your reasoning and an array of edit operations:
  
  ` +
    "```" +
    `json
  {
    "reasoning": "First explain your thinking process about how you're interpreting the edit pattern. Explain how you identified where the edits should be made in the original file. Describe any assumptions or decisions you made when determining the edit locations. You need to be very precise with the start and end lines! Reason why not 1 line before or after the start and end lines.",
    "operations": [
      {
        "startLine": 10,
        "endLine": 15,
        "replacement": "def process_file(filename):\n    # New implementation with better error handling\n    try:\n        with open(filename, 'r') as f:\n            return f.read()\n    except FileNotFoundError:\n        return None"
      },
      {
        "startLine": 25,
        "endLine": 25,
        "replacement": "logger.info(\"File processing completed\")"
      }
    ]
  }
  ` +
    "```" +
    `
  For lines that include "// ... existing code ...", do not include them in the replacement.
  Instead, identify the exact lines they represent in the original file and set the line 
  numbers accordingly. StartLine and endLine are 1-indexed.
  
  If the instruction indicates content should be appended to the file, set both startLine and endLine 
  to the maximum line number + 1, which will add the content at the end of the file.
  `
  );
}
