import React, { useState, useMemo, useEffect } from "react";
import { Tabs, Select, DropdownMenu, Button } from "@radix-ui/themes"; // Import DropdownMenu
import { PrevNodeFieldSelector } from "../common/PrevNodeFieldSelector/PrevNodeFieldSelector";
import { HTTPRequestConfig, HTTPRequestConfigParam } from "@lvjw/code-workflow-types";
import { extractParamsFromUrl } from "./helpers"; // 导入新的辅助函数
import KeyValueTable from "./components/KeyValueTable"; // 导入 KeyValueTable 组件

// KeyValueTable 组件代码已移至 ./components/KeyValueTable.tsx

const HTTP_METHODS = ["GET", "POST", "PUT", "PATCH", "DELETE"];
const BODY_TYPES = [
  { key: "none", label: "none" },
  { key: "form-data", label: "form-data" },
  { key: "x-www-form-urlencoded", label: "x-www-form-urlencoded" },
  { key: "json", label: "json" },
  { key: "xml", label: "xml" },
  { key: "raw-text", label: "raw-text" },
];

interface HTTPRequestConfigRendererProps {
  value?: HTTPRequestConfig;
  nodeId: string
  onChange?: (value: HTTPRequestConfig) => void;
  label?: string;
  defaultHeight?: number;
}

const HTTPRequestConfigRenderer = (props: HTTPRequestConfigRendererProps) => {
  //   // 只在挂载时初始化
  const initedRef = React.useRef(false);
  const [params, setParams] = useState<HTTPRequestConfigParam[]>([
    { type: "const", key: "", value: "", valuePath: [], valueLabelPath: [] },
  ]);
  const [queryParams, setQueryParams] = useState<HTTPRequestConfigParam[]>([
    { type: "const", key: "", value: "", valuePath: [], valueLabelPath: [] },
  ]);
  const [headers, setHeaders] = useState<HTTPRequestConfigParam[]>([
    { type: "const", key: "", value: "", valuePath: [], valueLabelPath: [] },
  ]);
  const [method, setMethod] = useState<string>("PATCH");
  const [url, setUrl] = useState<string>("");
  const [timeout, setTimeoutValue] = useState<number>(30);
  const [bodyType, setBodyType] = useState<string>("json");
  const [body, setBody] = useState<string>("");
  const [formData, setFormData] = useState<HTTPRequestConfigParam[]>([
    { type: "const", key: "", value: "", valuePath: [], valueLabelPath: [] },
  ]);
  const [urlencoded, setUrlencoded] = useState<HTTPRequestConfigParam[]>([
    { type: "const", key: "", value: "", valuePath: [], valueLabelPath: [] },
  ]);
  const [xml, setXml] = useState<string>("");

  // 只在挂载时用 props.value 初始化
  useEffect(() => {
    if (!initedRef.current && props.value) {
      try {
        const parsed = props.value as HTTPRequestConfig;
        setParams(
          parsed.params || [
            { type: "const", key: "", value: "", valuePath: [], valueLabelPath: [] },
          ]
        );
        setQueryParams(
          parsed.queryParams || [
            { type: "const", key: "", value: "", valuePath: [], valueLabelPath: [] },
          ]
        );
        setHeaders(
          parsed.headers || [
            { type: "const", key: "", value: "", valuePath: [], valueLabelPath: [] },
          ]
        );
        setMethod(parsed.method || "PATCH");
        setUrl(parsed.url || "");
        setTimeoutValue(parsed.timeout || 30);
        setBodyType(parsed.bodyType || "json");
        if (typeof parsed.body === "string") {
          setBody(parsed.body);
        } else if (Array.isArray(parsed.body)) {
          if (parsed.bodyType === "form-data") setFormData(parsed.body);
          if (parsed.bodyType === "x-www-form-urlencoded") setUrlencoded(parsed.body);
        }
        if (parsed.bodyType === "xml" && typeof parsed.body === "string") setXml(parsed.body);
      } catch {}
      initedRef.current = true;
    }
    // eslint-disable-next-line
  }, []);

  // 统一收集所有配置
  const getConfig = (override: Partial<HTTPRequestConfig> = {}) => {
    let bodyValue: string | HTTPRequestConfigParam[] = "";
    const o: Partial<
      HTTPRequestConfig & {
        formData?: HTTPRequestConfigParam[];
        urlencoded?: HTTPRequestConfigParam[];
        xml?: string;
      }
    > = override;
    const bt = o.bodyType ?? bodyType;
    if (bt === "form-data") bodyValue = (o.formData as HTTPRequestConfigParam[]) ?? formData;
    else if (bt === "x-www-form-urlencoded")
      bodyValue = (o.urlencoded as HTTPRequestConfigParam[]) ?? urlencoded;
    else if (bt === "json") bodyValue = o.body ?? body;
    else if (bt === "xml") bodyValue = (o.xml as string) ?? xml;
    else if (bt === "raw-text") bodyValue = o.body ?? body;
    else bodyValue = "";
    return {
      method: o.method ?? method,
      url: o.url ?? url,
      timeout: o.timeout ?? timeout,
      params: o.params ?? params,
      queryParams: o.queryParams ?? queryParams, // Add queryParams here
      headers: o.headers ?? headers,
      bodyType: bt,
      body: bodyValue,
    };
  };

  // 统一触发 onChange
  const triggerChange = (override = {}) => {
    if (props.onChange) {
      props.onChange(JSON.parse(JSON.stringify(getConfig(override))));
    }
  };

  // 识别 url 中的路径参数和查询参数，自动添加到 params 和 queryParams
  useEffect(() => {
    const handler = setTimeout(() => {
      const { pathParams: extractedPathParams, queryParams: extractedQueryParams } = extractParamsFromUrl(url);

      if (extractedPathParams.length > 0) {
        setParams((prevParams) => {
          let nextParams = [...prevParams];
          let changed = false;
          extractedPathParams.forEach((newParam) => {
            if (!nextParams.some((p) => p.key === newParam.key)) {
              const emptyIdx = nextParams.findIndex((p) => !p.key);
              if (emptyIdx !== -1) {
                nextParams[emptyIdx] = { ...nextParams[emptyIdx], key: newParam.key };
              } else {
                nextParams.push(newParam);
              }
              changed = true;
            }
          });
          if (changed) {
            props.onChange?.(JSON.parse(JSON.stringify(getConfig({ params: nextParams }))));
            return nextParams;
          }
          return prevParams;
        });
      }

      if (extractedQueryParams.length > 0) {
        setQueryParams((prevQueryParams) => {
          let nextQueryParams = [...prevQueryParams];
          let changed = false;
          extractedQueryParams.forEach((newQueryParam) => {
            if (!nextQueryParams.some((p) => p.key === newQueryParam.key)) {
              const emptyIdx = nextQueryParams.findIndex((p) => !p.key);
              if (emptyIdx !== -1) {
                nextQueryParams[emptyIdx] = { ...nextQueryParams[emptyIdx], key: newQueryParam.key, value: newQueryParam.value };
              } else {
                nextQueryParams.push(newQueryParam);
              }
              changed = true;
            }
          });
          if (changed) {
            props.onChange?.(JSON.parse(JSON.stringify(getConfig({ queryParams: nextQueryParams }))));
            return nextQueryParams;
          }
          return prevQueryParams;
        });
      }
    }, 1000); // 1秒延迟

    return () => {
      clearTimeout(handler);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [url, props.onChange]);

  // 事件处理函数
  const handleMethodChange = (val: string) => {
    setMethod(val);
    triggerChange({ method: val });
  };
  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setUrl(e.target.value);
    triggerChange({ url: e.target.value });
  };
  const handleTimeoutChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const v = Number(e.target.value);
    setTimeoutValue(v);
    triggerChange({ timeout: v });
  };
  const handleParamsChange = (rows: HTTPRequestConfigParam[]) => {
    setParams(rows);
    triggerChange({ params: rows });
  };
  const handleQueryParamsChange = (rows: HTTPRequestConfigParam[]) => {
    setQueryParams(rows);
    triggerChange({ queryParams: rows });
  };
  const handleHeadersChange = (rows: HTTPRequestConfigParam[]) => {
    setHeaders(rows);
    triggerChange({ headers: rows });
  };
  const handleBodyTypeChange = (val: string) => {
    setBodyType(val);
    triggerChange({ bodyType: val });
  };
  const handleBodyChange = (e: React.ChangeEvent<HTMLTextAreaElement> | string) => {
    const v = typeof e === "string" ? e : e.target.value;
    setBody(v);
    triggerChange({ body: v });
  };
  const handleFormDataChange = (rows: HTTPRequestConfigParam[]) => {
    setFormData(rows);
    triggerChange({ formData: rows });
  };
  const handleUrlencodedChange = (rows: HTTPRequestConfigParam[]) => {
    setUrlencoded(rows);
    triggerChange({ urlencoded: rows });
  };
  const handleXmlChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setXml(e.target.value);
    triggerChange({ xml: e.target.value });
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-md w-full min-w-xs">
      {/* 请求配置区域 */}
      <div className="mb-4 flex items-center gap-2">
        <Select.Root value={method} onValueChange={handleMethodChange}>
          <Select.Trigger style={{ width: 90, height: 32 }} />
          <Select.Content>
            {HTTP_METHODS.map((m) => (
              <Select.Item key={m} value={m}>
                {m}
              </Select.Item>
            ))}
          </Select.Content>
        </Select.Root>
        <input
          className="flex-1 border rounded px-2 py-1" // Removed min-w-0 to allow flex-1 to work better
          style={{ height: 32 }} // Added height for consistency
          placeholder="请求地址，输入'/'可选择变量"
          value={url}
          onChange={handleUrlChange}
        />
      </div>
      <div className="mb-6 flex items-center gap-2"> {/* New div for timeout and import */}
        <span className="text-gray-500">超时时长</span>
        <input
          type="number"
          className="w-20 border rounded px-2 py-1"
          style={{ height: 32 }} // Added height for consistency
          value={timeout}
          min={1}
          onChange={handleTimeoutChange}
        />
        <span className="text-gray-500">ms</span>
        <div className="ml-auto"> {/* Aligns dropdown to the right */}
          <DropdownMenu.Root>
            <DropdownMenu.Trigger>
              <Button 
                variant="ghost"
                style={{ height: 32 }} // Added height for consistency
              >
                导入
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item>导入 cURL</DropdownMenu.Item>
              <DropdownMenu.Item>导入 Swagger</DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu.Root>
        </div>
      </div>
      {/* Tabs 区域 */}
      <Tabs.Root defaultValue="params">
        <Tabs.List className="mb-4">
          <Tabs.Trigger value="params">Params</Tabs.Trigger>
          <Tabs.Trigger value="query">Query</Tabs.Trigger> {/* Add Query Tab Trigger */}
          <Tabs.Trigger value="body">Body</Tabs.Trigger>
          <Tabs.Trigger value="headers">Headers</Tabs.Trigger>
        </Tabs.List>
        <Tabs.Content value="params">
          <KeyValueTable
            rows={params}
            setRows={handleParamsChange}
            keyPlaceholder="参数名"
            valuePlaceholder="参数值"
            currentNodeId={props.nodeId}
          />
        </Tabs.Content>
        <Tabs.Content value="query"> {/* Add Query Tab Content */}
          <KeyValueTable
            rows={queryParams}
            setRows={handleQueryParamsChange}
            keyPlaceholder="参数名"
            valuePlaceholder="参数值"
            currentNodeId={props.nodeId}
          />
        </Tabs.Content>
        <Tabs.Content value="body">
          <Tabs.Root value={bodyType} onValueChange={handleBodyTypeChange} className="mb-2">
            <Tabs.List>
              {BODY_TYPES.map((t) => (
                <Tabs.Trigger key={t.key} value={t.key}>
                  {t.label}
                </Tabs.Trigger>
              ))}
            </Tabs.List>
            <Tabs.Content value="none">
              <div className="text-gray-400 mt-4">无请求体</div>
            </Tabs.Content>
            <Tabs.Content value="form-data">
              <div className="mt-4">
                <KeyValueTable
                  rows={formData}
                  setRows={handleFormDataChange}
                  keyPlaceholder="字段名"
                  valuePlaceholder="字段值"
                  currentNodeId={props.nodeId}
                />
              </div>
            </Tabs.Content>
            <Tabs.Content value="x-www-form-urlencoded">
              <div className="mt-4">
                <KeyValueTable
                  rows={urlencoded}
                  setRows={handleUrlencodedChange}
                  keyPlaceholder="参数名"
                  valuePlaceholder="参数值"
                  currentNodeId={props.nodeId}
                />
              </div>
            </Tabs.Content>
            <Tabs.Content value="json">
              <textarea
                rows={6}
                className="w-full border rounded p-2 font-mono mt-4"
                placeholder="请输入 JSON"
                value={body}
                onChange={handleBodyChange}
              />
            </Tabs.Content>
            <Tabs.Content value="xml">
              <textarea
                rows={6}
                className="w-full border rounded p-2 font-mono mt-4"
                placeholder="请输入 XML"
                value={xml}
                onChange={handleXmlChange}
              />
            </Tabs.Content>
            <Tabs.Content value="raw-text">
              <textarea
                rows={6}
                className="w-full border rounded p-2 font-mono mt-4"
                placeholder="请输入原始文本"
                value={body}
                onChange={handleBodyChange}
              />
            </Tabs.Content>
          </Tabs.Root>
        </Tabs.Content>
        <Tabs.Content value="headers">
          <KeyValueTable
            rows={headers}
            setRows={handleHeadersChange}
            keyPlaceholder="Header名"
            valuePlaceholder="Header值"
            currentNodeId={props.nodeId}
          />
        </Tabs.Content>
      </Tabs.Root>
      {/* 配置预览 */}
      {/* <div className="mt-6 bg-gray-50 rounded p-4 text-xs text-gray-700">
        <div className="font-bold mb-2">当前请求配置：</div>
        <pre className="overflow-x-auto whitespace-pre-wrap">
          {JSON.stringify(getConfig(), null, 2)}
        </pre>
      </div> */}
    </div>
  );
};

export default HTTPRequestConfigRenderer;
