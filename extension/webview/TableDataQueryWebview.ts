import * as vscode from "vscode";
import { ConnectionManager } from "../utils/connection/connection-manager";

export class TableDataQueryWebviewContainer {
  private panel: vscode.WebviewPanel | undefined;

  constructor(
    private context: vscode.ExtensionContext,
    private connectionManager: ConnectionManager
  ) {}

  public async openQueryConsole(): Promise<void> {
    try {
      // 如果面板已经存在，则激活它
      if (this.panel) {
        this.panel.reveal(vscode.ViewColumn.Active);
        return;
      }

      // 创建新的面板
      this.panel = vscode.window.createWebviewPanel(
        "queryConsole",
        "查询控制台",
        vscode.ViewColumn.Active,
        {
          enableScripts: true,
          retainContextWhenHidden: true,
        }
      );

      // 设置面板关闭时的处理函数
      this.panel.onDidDispose(() => {
        this.panel = undefined;
      });

      // 设置消息处理函数
      this.panel.webview.onDidReceiveMessage(async (message) => {
        switch (message.command) {
          case "getConnections":
            // 获取所有连接
            const connections = this.connectionManager.getConnections();
            this.panel?.webview.postMessage({
              command: "connectionsList",
              connections: connections.map((c) => ({
                id: c.id,
                name: c.name,
                type: c.type,
              })),
            });
            break;

          case "executeQuery":
            try {
              const { connectionId, query, databaseName, schemaName } = message;

              if (!connectionId) {
                this.panel?.webview.postMessage({
                  command: "queryError",
                  error: "请先选择数据库连接",
                });
                return;
              }

              // 执行查询
              const result = await this.connectionManager.executeQuery(
                connectionId,
                query,
                databaseName,
                schemaName
              );

              // 发送结果回 webview
              this.panel?.webview.postMessage({
                command: "queryResult",
                result: result,
              });
            } catch (error) {
              this.panel?.webview.postMessage({
                command: "queryError",
                error: error instanceof Error ? error.message : String(error),
              });
            }
            break;
        }
      });

      // 设置初始 HTML 内容
      this.panel.webview.html = this.getWebviewContent();
    } catch (error) {
      vscode.window.showErrorMessage(
        `Failed to open query console: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  private getWebviewContent(): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'unsafe-inline';">
        <title>查询控制台</title>
        <style>
          body {
            padding: 20px;
            color: var(--vscode-foreground);
            font-family: var(--vscode-font-family);
            background-color: var(--vscode-editor-background);
          }

          .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            gap: 20px;
          }

          .connection-container {
            display: flex;
            align-items: center;
            gap: 10px;
          }

          select {
            padding: 6px 10px;
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-input-foreground);
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 2px;
          }

          .editor-container {
            flex: 1;
            min-height: 200px;
          }

          .results-container {
            flex: 1;
            overflow: auto;
            border: 1px solid var(--vscode-panel-border);
          }

          textarea {
            width: 100%;
            height: 100%;
            padding: 10px;
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-input-foreground);
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            resize: none;
          }

          button {
            padding: 6px 14px;
            border: none;
            color: var(--vscode-button-foreground);
            background-color: var(--vscode-button-background);
            cursor: pointer;
            border-radius: 2px;
          }

          button:hover {
            background-color: var(--vscode-button-hoverBackground);
          }

          button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          table {
            width: 100%;
            border-collapse: collapse;
          }

          th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid var(--vscode-panel-border);
          }

          th {
            background-color: var(--vscode-editor-background);
            position: sticky;
            top: 0;
          }

          .error {
            color: var(--vscode-errorForeground);
            padding: 10px;
            background-color: var(--vscode-inputValidation-errorBackground);
            border: 1px solid var(--vscode-inputValidation-errorBorder);
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="connection-container">
            <select id="connectionSelect">
              <option value="">选择数据库连接...</option>
            </select>
          </div>
          <div class="editor-container">
            <textarea id="queryEditor" placeholder="输入 SQL 查询..."></textarea>
          </div>
          <div>
            <button id="executeButton" disabled>执行查询</button>
          </div>
          <div class="results-container" id="resultsContainer">
            <!-- 查询结果将在这里显示 -->
          </div>
        </div>

        <script>
          const vscode = acquireVsCodeApi();
          const connectionSelect = document.getElementById('connectionSelect');
          const queryEditor = document.getElementById('queryEditor');
          const executeButton = document.getElementById('executeButton');
          const resultsContainer = document.getElementById('resultsContainer');

          // 获取连接列表
          vscode.postMessage({
            command: 'getConnections'
          });

          // 监听连接选择变化
          connectionSelect.addEventListener('change', () => {
            executeButton.disabled = !connectionSelect.value;
          });

          // 监听消息
          window.addEventListener('message', event => {
            const message = event.data;
            switch (message.command) {
              case 'connectionsList':
                // 清空现有选项
                connectionSelect.innerHTML = '<option value="">选择数据库连接...</option>';
                
                // 添加连接选项
                message.connections.forEach(connection => {
                  const option = document.createElement('option');
                  option.value = connection.id;
                  option.textContent = \`\${connection.name} (\${connection.type})\`;
                  connectionSelect.appendChild(option);
                });
                break;

              case 'queryResult':
                const result = message.result;
                let html = '<table>';
                
                // 添加表头
                html += '<tr>';
                result.columns.forEach(column => {
                  html += \`<th>\${column}</th>\`;
                });
                html += '</tr>';

                // 添加数据行
                result.rows.forEach(row => {
                  html += '<tr>';
                  result.columns.forEach(column => {
                    const value = row[column];
                    html += \`<td>\${value === null ? 'NULL' : value}</td>\`;
                  });
                  html += '</tr>';
                });

                html += '</table>';
                resultsContainer.innerHTML = html;
                break;

              case 'queryError':
                resultsContainer.innerHTML = \`<div class="error">\${message.error}</div>\`;
                break;
            }
          });

          executeButton.addEventListener('click', () => {
            const query = queryEditor.value;
            const connectionId = connectionSelect.value;

            if (!connectionId) {
              resultsContainer.innerHTML = '<div class="error">请先选择数据库连接</div>';
              return;
            }

            if (!query) {
              resultsContainer.innerHTML = '<div class="error">请输入 SQL 查询</div>';
              return;
            }

            vscode.postMessage({
              command: 'executeQuery',
              connectionId: connectionId,
              query: query
            });
          });
        </script>
      </body>
      </html>
    `;
  }
}
