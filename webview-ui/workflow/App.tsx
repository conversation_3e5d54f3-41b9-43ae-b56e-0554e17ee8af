import React, { useEffect, useRef, useState } from "react";
import { Theme, Flex, Text, Button } from "@radix-ui/themes";
import { WorkflowCanvas } from "./WorkflowComponents/WorkflowCanvas/WorkflowCanvas";
import { WorkflowHeader } from "./WorkflowComponents/WorkflowHeader/WorkflowHeader";
import { WorkflowNodeTypeEnum } from "@lvjw/code-workflow-types";

declare global {
  interface Window {
    webviewInfo?: {
      viewType: string;
      title: string;
      panelId: string;
    };
    vscode: any;
  }
}

function App() {
  const [workflowPath, setWorkflowPath] = useState<string>('');
  const workflowRef = useRef<any>(null);

  useEffect(() => {
    console.log("App component mounted");

    // 通知 VSCode webview 获取数据
    const vscode = (window as any).vscode;
    if (vscode) {
      vscode.postMessage({ type: "getContent" });
      vscode.postMessage({ type: "getWorkflowPath" });
    } else {
      console.error("VSCode API not found");
    }

    // 监听来自 VSCode 的消息
    const messageHandler = (event: MessageEvent) => {
      const message = event.data;
      console.log("App received message:", message);

      switch (message.type) {
        case "update":
          workflowRef.current?.setGraph(JSON.parse(message.content))
          break;
        case "workflowPath":
          console.log("Received workflow path:", message.path);
          setWorkflowPath(message.path);
          break;
      }
    };

    window.addEventListener("message", messageHandler);

    return () => {
      window.removeEventListener("message", messageHandler);
    };
  }, []);

  const handleAddNode = (nodeType: WorkflowNodeTypeEnum) => {
      workflowRef.current?.addNode(nodeType)
  }

  const handleSave = () => {
      const graph = workflowRef.current?.getGrah()
      console.log("graph", graph)
      const content = JSON.stringify(graph, null, 2)
      const vscode = (window as any).vscode;
      if (vscode) {
        vscode.postMessage({
          type: "save",
          content: content,
        });
      } else {
        console.error("VSCode API not found");
      }
  }

  const handleRun = () => {
      const graph = workflowRef.current?.getGrah()
      // TODO 运行
  }

  return (
    <Theme className="w-full h-full">
      <div className="flex flex-col w-full h-full">
        <WorkflowHeader workflowPath={workflowPath} onAddNode={handleAddNode} onSave={handleSave} onRun={handleRun}/>
        <WorkflowCanvas ref={workflowRef}/>
      </div>
    </Theme>
  );
}

export default App;
