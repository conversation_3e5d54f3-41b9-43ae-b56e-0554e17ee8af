export const getRequirementAnalysisPrompt = (
    userRequirement: string,
    clarification: string
) => {
    return `<requirement>
    你是一名专业的 TypeORM 专家和 Node.js 开发者，负责根据用户需求生成对应的 TypeORM 代码。
    首先分析下方用户需求和已经补充的澄清信息，判断是否需要进一步澄清，如需要，则提出需要澄清的问题，如不需要，则返回整合后的 TypeORM 代码。
    用户并不一定具备 TypeORM 的编程经验，但用户具备业务知识，所以仅提问与需求相关的问题，编程细节不需要用户提供。
  </requirement>
  
  <user_requirement>${userRequirement}</user_requirement>
  
  ${clarification ? `<clarification>${clarification}</clarification>` : ""}
  
  <response_format_1>
    {
      "needsClarification": true,
      "questions": [
        "question1",
        "question2"
      ]
    }
  </response_format_1>
  
  <response_format_2>
    {
      "needsClarification": false,
      "requirement": "string"
    }
  </response_format_2>
  
  返回内容只能是 response_format_1 或 response_format_2 中的一个，不能包含其他内容。
  `;
};

export interface DatabaseTableInfo {
    name: string;
    comment: string;
    columns: DatabaseTableColunnInfo[];
    
}

export interface DatabaseTableColunnInfo {
    // 基本信息
    name: string;                 // 列名
    comment: string;              // 列注释
    type: string;                 // 数据类型
    isPrimaryKey: boolean;        // 是否为主键

    // TypeORM @Column 选项
    isNullable?: boolean;         // 是否可为空
    isUnique?: boolean;           // 是否唯一
    isGenerated?: boolean;        // 是否自动生成
    default?: any;                // 默认值
    length?: number;              // 字符串长度
    precision?: number;           // 数字精度
    scale?: number;               // 小数位数
    charset?: string;             // 字符集
    collation?: string;           // 排序规则
    enum?: any[];                 // 枚举值列表
    array?: boolean;              // 是否为数组

    // class-validator 验证规则
    validations?: {
        isEmail?: boolean;        // 是否为邮箱
        isUrl?: boolean;          // 是否为URL
        isDate?: boolean;         // 是否为日期
        min?: number;             // 最小值
        max?: number;             // 最大值
        minLength?: number;       // 最小长度
        maxLength?: number;       // 最大长度
        isInt?: boolean;          // 是否为整数
        isNumber?: boolean;       // 是否为数字
        isBoolean?: boolean;      // 是否为布尔值
        matches?: string;         // 正则表达式匹配
        isIn?: any[];            // 允许的值列表
        isNotEmpty?: boolean;     // 是否不能为空
    };

    // 关系选项
    foreignKey?: {
        referencedTable: string;  // 引用的表名
        referencedColumn: string; // 引用的列名
        onDelete?: string;        // 删除时的操作
        onUpdate?: string;        // 更新时的操作
    };
}

export const getGenerateTableCodePrompt = (
    userRequirement: string,
    tableList: DatabaseTableInfo[]
) => `<role>你是一名专业的 typeorm 专家和 Node.js 开发者。<role>
  <task>你将结合用户需求撰写 typeorm 的 Entity 代码</task>
  <context>
  下面是数据库表的信息：
  ${(tableList ?? []).map((table) => {
      return `
    <existed-table>
      <name>${table.name}</name>
      <comment>${table.comment}</comment>
      <columns>
        ${table.columns.map((column) => {
            return `<column>
            <name>${column.name}</name>
            <comment>${column.comment}</comment>
          </column>`;
        }).join('')}
      </columns>
    </existed-table>
    `;
  }).join('')}
  </context>
  
  <response>
  - 以 markdown 形式输出，markdown 中只包含代码设计相关的内容，保证代码块格式正确，不要参杂其他字符
  - 每个 Entity 使用一个单独的代码块，代码格式是 ts
  - 代码中只可以引入 typeorm 和 class-validator
  - 每个 Entity 要带有中文注释，注释不宜过长，注释写在 @Entity 装饰器中
  - 每个 Entity 的 Column 要带有中文注释，注释不宜过长，注释写在 @Column 装饰器中
  - 新设计的表不要与 existed-table 存在同名的表

    <example>
      用户实体
` + '```ts' + `
      import { Entity, PrimaryGeneratedColumn, Column, BaseEntity } from "typeorm";
      import { Min, Max, IsEmail } from "class-validator";
      @Entity({
        name: "user",
        comment: "User table"
      })
      export class User extends BaseEntity {
        @PrimaryGeneratedColumn() 
        id: number;

        @Column({ unique: true ,comment: "Email address of the user"})
        @IsEmail()
        email: string;

        @Column({ comment: " name of the user" })
        name: string;
      }
` + '```' + `
          
      文章实体
` + '```ts' + `
      import { Entity, PrimaryGeneratedColumn, Column, BaseEntity } from "typeorm";
      @Entity({
        name: "user",
        comment: "User table"
      })
      export class Artical extends BaseEntity {
        @PrimaryGeneratedColumn() 
        id: number;

        @Column({ comment: " name of the article" })
        name: string;
      }
` + '```' + `
    </example>
  </response>

  <user_requirement>${userRequirement}</user_requirement>
`;
