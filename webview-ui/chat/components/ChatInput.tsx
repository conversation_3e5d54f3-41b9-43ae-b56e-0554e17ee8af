import React, { useEffect } from 'react';
import { Text<PERSON>rea, IconButton, Select } from '@radix-ui/themes';
import { PaperPlaneIcon } from '@radix-ui/react-icons';
import { QuickActions } from './QuickActions';

interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  onKeyPress: (e: React.KeyboardEvent) => void;
  isLoading: boolean;
  textareaRef: React.RefObject<HTMLTextAreaElement | null>;
  onInsertText: (text: string) => void;
}

export const ChatInput: React.FC<ChatInputProps> = ({
  value,
  onChange,
  onSend,
  onKeyPress,
  isLoading,
  textareaRef,
  onInsertText
}) => {
  const [selectedModel, setSelectedModel] = React.useState('gpt-3.5-turbo');

  // 自动调整textarea高度
  useEffect(() => {
    if (textareaRef.current) {
      const textarea = textareaRef.current;
      // 重置高度以获取正确的scrollHeight
      textarea.style.height = '60px'; // 最小高度（两行）
      
      // 计算新高度
      const scrollHeight = textarea.scrollHeight;
      const maxHeight = window.innerHeight * 0.4; // 40%视口高度
      
      // 设置新高度，在最小和最大值之间
      const newHeight = Math.min(Math.max(scrollHeight, 60), maxHeight);
      textarea.style.height = `${newHeight}px`;
    }
  }, [value, textareaRef]);

  return (
    <div className="px-0 py-2 space-y-2">
      {/* 上部分：TextArea */}
      <div className="relative">
        <TextArea
          ref={textareaRef}
          placeholder="输入你的问题..."
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyPress={onKeyPress}
          rows={2}
          className="w-full resize-none vscode-input rounded-md overflow-hidden"
          style={{
            resize: 'none',
            minHeight: '60px',
            maxHeight: '40vh',
            lineHeight: '1.5',
            backgroundColor: 'var(--vscode-input-background)',
            borderColor: 'var(--vscode-input-border)',
            color: 'var(--vscode-input-foreground)',
            outline: 'none'
          }}
        />
      </div>
      
      {/* 下部分：左侧快捷按钮，右侧模型选择器和发送按钮 */}
      <div className="flex items-center justify-between gap-4">
        {/* 左侧：快捷按钮 */}
        <div className="flex-1">
          <QuickActions onInsertText={onInsertText} />
        </div>
        
        {/* 右侧：模型选择器和发送按钮 */}
        <div className="flex items-center gap-2">
          <Select.Root value={selectedModel} onValueChange={setSelectedModel}>
            <Select.Trigger 
              className="vscode-bg-secondary vscode-border vscode-text-primary hover:vscode-hover min-w-24 text-sm"
              style={{
                backgroundColor: 'var(--vscode-sideBar-background)',
                borderColor: 'var(--vscode-input-border)',
                color: 'var(--vscode-sideBar-foreground)',
                outline: 'none',
                fontSize: '14px'
              }}
            >
            </Select.Trigger>
            <Select.Content 
              className="vscode-bg-secondary vscode-border"
              style={{
                backgroundColor: 'var(--vscode-sideBar-background)',
                borderColor: 'var(--vscode-input-border)',
                color: 'var(--vscode-sideBar-foreground)'
              }}
            >
              <Select.Item 
                value="gpt-4" 
                className="vscode-text-primary hover:vscode-hover focus:vscode-hover text-sm"
                style={{
                  backgroundColor: 'transparent',
                  color: 'var(--vscode-sideBar-foreground)',
                  fontSize: '14px'
                }}
              >
                GPT-4
              </Select.Item>
              <Select.Item 
                value="gpt-3.5-turbo" 
                className="vscode-text-primary hover:vscode-hover focus:vscode-hover text-sm"
                style={{
                  backgroundColor: 'transparent',
                  color: 'var(--vscode-sideBar-foreground)',
                  fontSize: '14px'
                }}
              >
                GPT-3.5 Turbo
              </Select.Item>
              <Select.Item 
                value="claude-3" 
                className="vscode-text-primary hover:vscode-hover focus:vscode-hover text-sm"
                style={{
                  backgroundColor: 'transparent',
                  color: 'var(--vscode-sideBar-foreground)',
                  fontSize: '14px'
                }}
              >
                Claude-3
              </Select.Item>
            </Select.Content>
          </Select.Root>
          
          <IconButton
            size="2"
            onClick={onSend}
            disabled={!value.trim() || isLoading}
            className={`rounded-full transition-all duration-200 hover:scale-105 ${
              value.trim() && !isLoading
                ? 'vscode-button hover:vscode-button'
                : 'vscode-button'
            }`}
            style={{
              opacity: value.trim() && !isLoading ? 1 : 0.7,
              cursor: value.trim() && !isLoading ? 'pointer' : 'not-allowed',
              backgroundColor: 'var(--vscode-button-background)',
              color: 'var(--vscode-button-foreground)'
            }}
          >
            <PaperPlaneIcon />
          </IconButton>
        </div>
      </div>
    </div>
  );
};