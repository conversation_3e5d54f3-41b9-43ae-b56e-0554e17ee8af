import fs from 'fs/promises'
import path from 'path'
import pdf from 'pdf-parse'
import mammoth from 'mammoth'
import ExcelJS from 'exceljs'
import * as chardet from "jschardet"
import * as iconv from 'iconv-lite'
import { isBinaryFile } from "isbinaryfile"

export interface ReadFileOptions {
    should_read_entire_file?: boolean
}

export async function readFile(
    filePath: string,
    startLineOneIndexed?: number,
    endLineOneIndexedInclusive?: number,
): Promise<[boolean, string]> {
    try {
        await fs.access(filePath)
    } catch (error) {
        throw new Error(`File not found: ${filePath}`)
    }

    const fileExtension = path.extname(filePath).toLowerCase()

    switch (fileExtension) {
        case '.pdf':
            return extractTextFromPDF(filePath)
        case '.docx':
            return extractTextFromDOCX(filePath)
        case '.ipynb':
            return extractTextFromIPYNB(filePath)
        case '.xlsx':
            return extractTextFromExcel(filePath)
        default:
            const fileBuffer = await fs.readFile(filePath)
            if (fileBuffer.byteLength > 20 * 1000 * 1024) {
                // 20MB limit (20 * 1000 * 1024 bytes, decimal MB)
                throw new Error(`File is too large to read into context.`)
            }
            const encoding = await detectEncoding(fileBuffer, fileExtension)
            return [true, iconv.decode(fileBuffer, encoding)]
    }
}

async function extractTextFromPDF(filePath: string): Promise<[boolean, string]> {
    const dataBuffer = await fs.readFile(filePath)
    const data = await pdf(dataBuffer)
    return [true, data.text]
}

async function extractTextFromDOCX(filePath: string): Promise<[boolean, string]> {
    const result = await mammoth.extractRawText({ path: filePath })
    return [true, result.value]
}

async function extractTextFromIPYNB(filePath: string): Promise<[boolean, string]> {
    const fileBuffer = await fs.readFile(filePath)
    const encoding = await detectEncoding(fileBuffer)
    const data = iconv.decode(fileBuffer, encoding)
    const notebook = JSON.parse(data)
    let extractedText = ''

    for (const cell of notebook.cells) {
        if ((cell.cell_type === 'markdown' || cell.cell_type === 'code') && cell.source) {
            extractedText += cell.source.join('\n') + '\n'
        }
    }

    return [true, extractedText]
}

/**
 * Format the data inside Excel cells
 */
function formatCellValue(cell: ExcelJS.Cell): string {
    const value = cell.value
    if (value === null || value === undefined) {
        return ''
    }

    // Handle error values (#DIV/0!, #N/A, etc.)
    if (typeof value === 'object' && 'error' in value) {
        return `[Error: ${value.error}]`
    }

    // Handle dates - ExcelJS can parse them as Date objects
    if (value instanceof Date) {
        return value.toISOString().split('T')[0] // Just the date part
    }

    // Handle rich text
    if (typeof value === 'object' && 'richText' in value) {
        return value.richText.map(rt => rt.text).join('')
    }

    // Handle hyperlinks
    if (typeof value === 'object' && 'text' in value && 'hyperlink' in value) {
        return `${value.text} (${value.hyperlink})`
    }

    // Handle formulas - get the calculated result
    if (typeof value === 'object' && 'formula' in value) {
        if ('result' in value && value.result !== undefined && value.result !== null) {
            return value.result.toString()
        } else {
            return `[Formula: ${value.formula}]`
        }
    }

    return value.toString()
}

/**
 * Extract and format text from xlsx files
 */
async function extractTextFromExcel(filePath: string): Promise<[boolean, string]> {
    const workbook = new ExcelJS.Workbook()
    let excelText = ''

    try {
        await workbook.xlsx.readFile(filePath)

        workbook.eachSheet((worksheet, sheetId) => {
            // Skip hidden sheets
            if (worksheet.state === 'hidden' || worksheet.state === 'veryHidden') {
                return
            }

            excelText += `--- Sheet: ${worksheet.name} ---\n`

            worksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
                // Optional: limit processing for very large sheets
                if (rowNumber > 50000) {
                    excelText += `[... truncated at row ${rowNumber} ...]\n`
                    return false
                }

                const rowTexts: string[] = []
                let hasContent = false

                row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
                    const cellText = formatCellValue(cell)
                    if (cellText.trim()) {
                        hasContent = true
                    }
                    rowTexts.push(cellText)
                })

                // Only add rows with actual content
                if (hasContent) {
                    excelText += rowTexts.join('\t') + '\n'
                }

                return true
            })

            excelText += '\n' // Blank line between sheets
        })

        return [true, excelText.trim()]
    } catch (error: any) {
        console.error(`Error extracting text from Excel ${filePath}:`, error)
        return [false, `Failed to extract text from Excel: ${error.message}`]
    }
}

export async function detectEncoding(fileBuffer: Buffer, fileExtension?: string): Promise<string> {
	const detected = chardet.detect(fileBuffer)
	if (typeof detected === "string") {
		return detected
	} else if (detected && (detected as any).encoding) {
		return (detected as any).encoding
	} else {
		if (fileExtension) {
			const isBinary = await isBinaryFile(fileBuffer).catch(() => false)
			if (isBinary) {
				throw new Error(`Cannot read text for file type: ${fileExtension}`)
			}
		}
		return "utf8"
	}
}
