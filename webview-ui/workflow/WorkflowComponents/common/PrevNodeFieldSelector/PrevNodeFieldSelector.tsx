import React from 'react';
import Cascader from '../../../LibComponents/Cascader';
import { usePrevNodeFieldSelector } from './usePrevNodeFieldSelector';
import { PrevNodeFieldSelectorProps } from './interface';


export const PrevNodeFieldSelector: React.FC<PrevNodeFieldSelectorProps> = ({
  currentNodeId,
  valuePath = [],
  valueLabelPath = [],
  onChange,
  triggerLabel = "选择属性..."
}) => {
  const { cascaderData } = usePrevNodeFieldSelector(currentNodeId);

  return (
    <Cascader
      data={cascaderData}
      defaultSelectedPath={valuePath}
      defaultSelectedLabelPath={valueLabelPath}
      triggerLabel={triggerLabel}
      onChange={onChange}
    />
  );
};
