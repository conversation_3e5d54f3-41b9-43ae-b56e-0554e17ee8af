import { ReactFlowNodeConfig, WorkflowNodeInputField } from "@lvjw/code-workflow-types";

export interface WorkflowNodeInputsProps {
  inputs: WorkflowNodeInputField[];
  node: ReactFlowNodeConfig;
}

export interface WorkflowNodeInputFieldProps {
  input: WorkflowNodeInputField;
  node: ReactFlowNodeConfig;
}

export interface WorkflowInputFieldDialogProps {
  input: WorkflowNodeInputField;
  node: ReactFlowNodeConfig;
  onSave?: (form: Partial<WorkflowNodeInputField>) => Promise<void>;
}

export interface WorkflowNodeInputFieldRenderProps {
  input: WorkflowNodeInputField;
  node: ReactFlowNodeConfig;
}


export interface StringWithLLMRendererProps {
  input: WorkflowNodeInputField;
  node: ReactFlowNodeConfig;
  value?: string;
  onChange?: (value: string) => void;
}
