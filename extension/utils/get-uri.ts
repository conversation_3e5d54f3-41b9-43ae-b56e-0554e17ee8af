import { Uri, Webview } from "vscode";

/**
 * 一个辅助函数，用于获取给定文件或资源的 webview URI。
 *
 * @remarks 此 URI 可以在 webview 的 HTML 中用作指向给定文件/资源的链接。
 *
 * @param webview 扩展 webview 的引用
 * @param extensionUri 包含扩展的目录的 URI
 * @param pathList 表示文件/资源路径的字符串数组
 * @returns 指向文件/资源的 URI
 */
export function getUri(webview: Webview, extensionUri: Uri, pathList: string[]) {
  return webview.asWebviewUri(Uri.joinPath(extensionUri, ...pathList));
}
