import { Node } from 'pocketflow'
import { callLlm } from '../utils/callLlm'
import { CodingAgentSharedStore, History } from '../types'
import { genSystemPrompt } from '../utils/genSystemPrompt'
import { extractJsonBlockFromMarkdownText } from '../utils/common'
import { McpHub } from '../../../services/mcp/McpHub'
import { Output } from '../types'

interface PrepResult {
    userQuery: string
    mcpHub?: McpHub
    history: History[]
    output: Output
}

interface MainDecisionAgentNodeExecResult {
    tool: string
    reason: string
    params: Record<string, any>
}

export class MainDecisionAgentNode extends Node<CodingAgentSharedStore> {
    async prep(shared: CodingAgentSharedStore): Promise<PrepResult> {
        return {
            userQuery: shared.userQuery,
            history: shared.history,
            output: shared.output,
            mcpHub: shared.mcpHub,
        }
    }

    async exec(
        prepResult: PrepResult,
    ): Promise<{ tool: string; reason: string; params: Record<string, any> }> {


        if (prepResult.mcpHub) {
            const pWaitFor = await require('p-wait-for').default
            await pWaitFor(() => prepResult.mcpHub!.isConnecting !== true, {
                timeout: 10_000,
            }).catch(() => {
                console.error('MCP servers failed to connect in time')
            })
            console.log('mcp hub 加载成功')
        }

        const systemPrompt = genSystemPrompt('/', prepResult.mcpHub?.getServers() ?? [])
        const prompts = [
            {
                "role": "system",
                "content": systemPrompt
            },
            {
                "role": "user",
                "content": prepResult.userQuery
            },
            ...(prepResult.history ?? [])
        ]


        const response = await callLlm(prompts )

        prepResult.output.text(response)

        const jsonResult = extractJsonBlockFromMarkdownText(response)

        if (!jsonResult) {
            console.error(
                `MainDecisionAgent - exec: 解析 LLM 响应失败，返回内容: ${response}`,
            )
            throw new Error('Failed to parse LLM response')
        } else {    
            
            return jsonResult as unknown as MainDecisionAgentNodeExecResult
        }
    }

    async post(
        shared: CodingAgentSharedStore,
        prepResult: PrepResult,
        execResult: MainDecisionAgentNodeExecResult,
    ): Promise<string> {
      
        const newAction = {
            tool: execResult.tool,
            params: execResult.params,
            result: null,
            success: false,
            timestamp: Date.now(),
        }

        // 修改当前任务
        shared.currentAction = newAction

        // 添加历史
        shared.history.push({
            role: 'assistant',
            content: JSON.stringify(execResult)
        })

        return execResult.tool
    }
}
