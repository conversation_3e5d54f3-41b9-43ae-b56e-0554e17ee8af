export interface ConnectionProvider {

    createConnection(connection: DatabaseConnectionConfig): Promise<any>;
    
    closeConnection(client: any): Promise<void>;
    
    getDatabases(client: any): Promise<{ name: string }[]>;
    
    getSchemas(client: any, database: string): Promise<{ name: string }[]>;
    
    getTables(
        client: any, 
        database: string, 
        schema?: string
    ): Promise<{ name: string, type: 'table' | 'view', comment?: string }[]>;
    
    getColumns(
        client: any, 
        database: string, 
        table: string, 
        schema?: string
    ): Promise<{ 
        name: string, 
        type: string, 
        isPrimaryKey: boolean, 
        isForeignKey: boolean,
        references?: { table: string, column: string },
        comment?: string
    }[]>;
    
    executeQuery(
        client: any, 
        query: string, 
        database?: string, 
        schema?: string
    ): Promise<{ 
        columns: string[], 
        rows: any[],
        rowCount: number
    }>;
}

export interface DatabaseConnectionConfig {
    id: string;
    name: string;
    type: 'MySQL' | 'PostgreSQL' | 'SQLite';

    // Standard connection properties
    host?: string;
    port?: number;
    database?: string;
    username?: string;
    password?: string;

    // SQLite specific properties
    filePath?: string;

    // Additional properties
    options?: Record<string, any>;

    // 临时属性，用于表单处理
    needFileSelection?: boolean;
}