import React from "react";
import { NodeProps } from '@xyflow/react';
import { useSetAtom } from 'jotai'; // Import useSetAtom
import { isTableEditDrawerOpenAtom, editingTableIdAtom } from './atoms'; // Import atoms
import { IconButton } from '@radix-ui/themes'; // Assuming you'll use Radix IconButton
import { Pencil2Icon } from '@radix-ui/react-icons'; // Assuming you'll use Radix Icons
import { Table } from "./interface";



// TODO 修复一下这个类型
export const ERNode: React.FC<NodeProps<any>> = (props) => {
  console.log('===', props)
  const table = props.data as Table;
  const setIsTableEditDrawerOpen = useSetAtom(isTableEditDrawerOpenAtom);
  const setEditingTableId = useSetAtom(editingTableIdAtom);

  const handleEditClick = () => {
    console.log(props.id, '====')
    setEditingTableId(props.id); // or table.name, depending on your node ID structure
    setIsTableEditDrawerOpen(true);
  };

  return (
    <div
      className="rounded-md shadow-md"
      style={{
        background: '#fff',
        color: '#222',
        fontSize: 14,
        border: '1.5px solid #bbb',
        overflow: 'hidden',
        fontFamily: 'Menlo, monospace',
        padding: 0,
        boxSizing: 'border-box',
        width: 'auto',
      }}
    >
      {/* 顶部：表名和注释 */}
      <div
        className="flex items-center justify-between px-3 py-2 border-b" // Added justify-between
        style={{ borderColor: '#eee', background: '#fff' }}
      >
        <div className="flex items-center"> {/* Group left items */}
          <span className="mr-2" style={{ fontSize: 18 }}>📋</span>
          <span className="font-bold text-base text-black mr-2">{table.name}</span>
          <span className="text-xs" style={{ color: '#888', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
            / {table.type === 'table' ? 'User table' : 'View'}
          </span>
        </div>
        <IconButton size="1" variant="ghost" onClick={handleEditClick} style={{ cursor: 'pointer' }}>
          <Pencil2Icon width="14" height="14" />
        </IconButton>
      </div>
      {/* 字段区 */}
      <div className="py-1">
        {table.columns.map((column) => {
          const isPK = column.isPrimaryKey;
          return (
            <div
              key={column.name}
              className={`flex items-center px-3 py-1 ${isPK ? 'bg-[#f7f7e0]' : ''}`}
              style={{ borderBottom: '1px solid #f0f0f0', fontWeight: isPK ? 600 : 400 }}
            >
              {/* 字段名和注释 */}
              <span className={`mr-2 ${isPK ? 'text-yellow-700' : 'text-black'} `} style={{ minWidth: 70 }}>
                {column.name}{isPK ? '🔑': null} 
              </span>
              <span className="text-xs flex-1" style={{ color: '#888', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                {column.comment ? `/* ${column.comment} */` : ''}
              </span>
              {/* 字段类型 */}
              <span className="text-xs ml-2" style={{ color: '#444', minWidth: 60, textAlign: 'right' }}>
                {column.type.replace('character varying', 'varchar')}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
};