# AI编程助手功能说明

## 功能概述

本扩展新增了AI编程助手功能，可以帮助开发者解决编程问题、代码审查、优化建议等。

## 功能特性

### 1. 侧边栏聊天界面
- 在VS Code左侧活动栏中新增了"AI编程助手"图标
- 点击后可以看到AI聊天界面
- 支持实时对话，AI会根据你的问题提供专业的编程建议

### 2. 独立聊天窗口
- 通过命令面板执行"打开AI聊天"命令可以打开独立的聊天窗口
- 窗口支持拖拽和调整大小

### 3. 智能对话
- AI助手专门针对编程问题进行了优化
- 支持代码审查、bug修复、性能优化等建议
- 回答会尽量包含具体的代码示例

## 使用方法

### 配置API密钥
1. 打开VS Code设置（Ctrl/Cmd + ,）
2. 搜索"Flow Editor"
3. 在"Openai Api Key"字段中输入你的OpenAI API密钥

### 开始聊天
1. 点击左侧活动栏的"AI编程助手"图标（机器人图标）
2. 在聊天界面中输入你的问题
3. 按Enter键或点击"发送"按钮
4. AI会分析你的问题并提供专业建议

### 使用技巧
- 描述问题时尽量详细，包含相关的代码片段
- 可以询问特定技术栈的最佳实践
- 可以请求代码审查和优化建议
- 支持多轮对话，可以基于之前的回答继续提问

## 示例对话

**用户**: 如何在React中优化组件性能？

**AI助手**: 在React中优化组件性能有以下几种方法：

1. **使用React.memo**：
```jsx
const MyComponent = React.memo(({ name, age }) => {
  return <div>{name} is {age} years old</div>;
});
```

2. **使用useMemo缓存计算结果**：
```jsx
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(a, b);
}, [a, b]);
```

3. **使用useCallback缓存函数**：
```jsx
const memoizedCallback = useCallback(() => {
  doSomething(a, b);
}, [a, b]);
```

## 注意事项

- 需要有效的OpenAI API密钥才能使用
- 网络连接需要能够访问OpenAI API
- API调用会产生费用，请合理使用
- 建议在设置中配置API密钥后重启VS Code

## 故障排除

### 无法发送消息
1. 检查API密钥是否正确配置
2. 检查网络连接
3. 查看VS Code开发者控制台的错误信息

### AI回复异常
1. 确认API密钥有足够的额度
2. 检查OpenAI服务状态
3. 尝试重新发送消息

---

如有其他问题，请查看VS Code开发者控制台的详细错误信息。