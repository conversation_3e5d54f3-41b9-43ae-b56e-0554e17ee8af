// VS Code 主题检测和应用脚本
(function() {
  'use strict';

  // 检测当前VS Code主题
  function detectVSCodeTheme() {
    // 尝试从VS Code API获取主题信息
    if (window.acquireVsCodeApi) {
      const vscode = window.acquireVsCodeApi();
      
      // 监听主题变化消息
      window.addEventListener('message', event => {
        const message = event.data;
        if (message.type === 'themeChanged') {
          applyTheme(message.theme);
        }
      });
      
      // 请求当前主题
      vscode.postMessage({ type: 'getTheme' });
    }
    
    // 检测body的类名或样式来判断主题
    const body = document.body;
    const computedStyle = window.getComputedStyle(body);
    const backgroundColor = computedStyle.backgroundColor;
    
    // 根据背景色判断主题类型
    if (backgroundColor) {
      const rgb = backgroundColor.match(/\d+/g);
      if (rgb) {
        const [r, g, b] = rgb.map(Number);
        const brightness = (r * 299 + g * 587 + b * 114) / 1000;
        
        if (brightness < 128) {
          // 深色主题
          applyTheme('dark');
        } else {
          // 浅色主题
          applyTheme('light');
        }
      }
    }
    
    // 检查是否有VS Code特定的类名
    if (body.classList.contains('vscode-light')) {
      applyTheme('light');
    } else if (body.classList.contains('vscode-dark')) {
      applyTheme('dark');
    } else if (body.classList.contains('vscode-high-contrast')) {
      applyTheme('high-contrast');
    }
  }

  // 应用主题
  function applyTheme(theme) {
    const body = document.body;
    
    // 移除现有主题类
    body.classList.remove('vscode-light', 'vscode-dark', 'vscode-high-contrast');
    body.removeAttribute('data-vscode-theme-kind');
    
    // 应用新主题
    switch (theme) {
      case 'light':
        body.classList.add('vscode-light');
        body.setAttribute('data-vscode-theme-kind', 'vscode-light');
        break;
      case 'dark':
        body.classList.add('vscode-dark');
        body.setAttribute('data-vscode-theme-kind', 'vscode-dark');
        break;
      case 'high-contrast':
        body.classList.add('vscode-high-contrast');
        body.setAttribute('data-vscode-theme-kind', 'vscode-high-contrast');
        break;
      default:
        // 默认深色主题
        body.classList.add('vscode-dark');
        body.setAttribute('data-vscode-theme-kind', 'vscode-dark');
    }
  }

  // 监听系统主题变化
  function watchSystemTheme() {
    if (window.matchMedia) {
      const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
      
      darkModeQuery.addListener((e) => {
        if (!document.body.hasAttribute('data-vscode-theme-kind')) {
          // 只有在没有明确设置VS Code主题时才使用系统主题
          applyTheme(e.matches ? 'dark' : 'light');
        }
      });
      
      // 初始检查
      if (!document.body.hasAttribute('data-vscode-theme-kind')) {
        applyTheme(darkModeQuery.matches ? 'dark' : 'light');
      }
    }
  }

  // 初始化
  function init() {
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        detectVSCodeTheme();
        watchSystemTheme();
      });
    } else {
      detectVSCodeTheme();
      watchSystemTheme();
    }
  }

  // 启动主题检测
  init();
})();