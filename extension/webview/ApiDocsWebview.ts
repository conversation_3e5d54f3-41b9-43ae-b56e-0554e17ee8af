import * as vscode from "vscode";
import * as path from "path";
import * as fs from "fs";
import axios from "axios";

export class ApiDocsWebviewContainer {
  private _context: vscode.ExtensionContext;
  private _panel: vscode.WebviewPanel | undefined;

  constructor(context: vscode.ExtensionContext) {
    this._context = context;
  }

  public async openApiDocsWebviewPanel() {
    try {
      // 获取工作区根目录
      const workspaceFolders = vscode.workspace.workspaceFolders;
      if (!workspaceFolders) {
        vscode.window.showErrorMessage("请先打开一个工作区");
        return;
      }

      // 检查文档文件是否存在
      const docsPath = path.join(
        workspaceFolders[0].uri.fsPath,
        "src",
        "docs",
        "openapi.json"
      );
      if (!fs.existsSync(docsPath)) {
        const result = await vscode.window.showWarningMessage(
          "API 文档不存在，请先运行 npm run generate 生成文档",
          "运行命令"
        );

        if (result === "运行命令") {
          const terminal = vscode.window.createTerminal("Generate API Docs");
          terminal.sendText("npm run generate");
          terminal.show();
        }
        return;
      }

      // 如果已经有面板，则显示它
      if (this._panel) {
        this._panel.reveal();
        return;
      }

      // 创建新的面板
      this._panel = vscode.window.createWebviewPanel(
        "apiDocsView",
        "API 文档",
        vscode.ViewColumn.One,
        {
          enableScripts: true,
        }
      );

      // 设置 webview 内容
      this._panel.webview.html = this.getWebviewContent(docsPath);

      // 处理来自 webview 的消息
      this._panel.webview.onDidReceiveMessage(async (message) => {
        if (message.type === "apiRequest") {
          try {
            const response = await axios({
              method: message.method,
              url: `http://localhost:3058${message.path}`,
              headers: message.headers,
              data: message.body,
              params: message.params,
            });

            this._panel?.webview.postMessage({
              type: "apiResponse",
              requestId: message.requestId,
              data: response.data,
              status: response.status,
              headers: response.headers,
            });
          } catch (error) {
            this._panel?.webview.postMessage({
              type: "apiError",
              requestId: message.requestId,
              error: error instanceof Error ? error.message : String(error),
            });
          }
        }
      });

      // 处理面板关闭事件
      this._panel.onDidDispose(
        () => {
          this._panel = undefined;
        },
        null,
        this._context.subscriptions
      );
    } catch (error) {
      console.error(
        "打开 API 文档失败",
        error instanceof Error ? error : new Error(String(error))
      );
      vscode.window.showErrorMessage("打开 API 文档失败");
    }
  }

  private getWebviewContent(docsPath: string): string {
    try {
      const apiDocs = fs.readFileSync(docsPath, "utf-8");
      const jsonData = JSON.parse(apiDocs);

      // 获取 Swagger UI 资源的 URI
      const swaggerUiCssUri = this._panel?.webview.asWebviewUri(
        vscode.Uri.joinPath(
          this._context.extensionUri,
          "resources",
          "swagger-ui.css"
        )
      );
      const swaggerUiBundleUri = this._panel?.webview.asWebviewUri(
        vscode.Uri.joinPath(
          this._context.extensionUri,
          "resources",
          "swagger-ui-bundle.js"
        )
      );
      const swaggerUiStandalonePresetUri = this._panel?.webview.asWebviewUri(
        vscode.Uri.joinPath(
          this._context.extensionUri,
          "resources",
          "swagger-ui-standalone-preset.js"
        )
      );

      return `<!DOCTYPE html>
            <html lang="zh-CN">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>API 文档</title>
                <link rel="stylesheet" type="text/css" href="${swaggerUiCssUri}" />
                <style>
                    body {
                        margin: 0;
                        padding: 0;
                        background-color: var(--vscode-editor-background);
                    }
                    .swagger-ui {
                        background-color: var(--vscode-editor-background);
                    }
                    .swagger-ui .opblock {
                        background-color: var(--vscode-editor-background);
                        border-color: var(--vscode-panel-border);
                    }
                    .swagger-ui .opblock .opblock-summary {
                        border-color: var(--vscode-panel-border);
                    }
                    .swagger-ui .opblock .opblock-summary-method {
                        background-color: var(--vscode-charts-blue);
                    }
                    .swagger-ui .opblock-get .opblock-summary-method {
                        background-color: var(--vscode-charts-blue);
                    }
                    .swagger-ui .opblock-post .opblock-summary-method {
                        background-color: var(--vscode-charts-green);
                    }
                    .swagger-ui .opblock-put .opblock-summary-method {
                        background-color: var(--vscode-charts-orange);
                    }
                    .swagger-ui .opblock-delete .opblock-summary-method {
                        background-color: var(--vscode-charts-red);
                    }
                    .swagger-ui .opblock-patch .opblock-summary-method {
                        background-color: var(--vscode-charts-purple);
                    }
                    .swagger-ui .info .title {
                        color: var(--vscode-editor-foreground);
                    }
                    .swagger-ui .info .description {
                        color: var(--vscode-descriptionForeground);
                    }
                    .swagger-ui .opblock-description-wrapper p {
                        color: var(--vscode-descriptionForeground);
                    }
                    .swagger-ui .parameter__name {
                        color: var(--vscode-symbolIcon-parameterForeground);
                    }
                    .swagger-ui .parameter__type {
                        color: var(--vscode-symbolIcon-typeForeground);
                    }
                    .swagger-ui .parameter__required {
                        color: var(--vscode-charts-red);
                    }
                    .swagger-ui .response-col_status {
                        color: var(--vscode-symbolIcon-constantForeground);
                    }
                    .swagger-ui .response-col_description {
                        color: var(--vscode-descriptionForeground);
                    }
                </style>
            </head>
            <body>
                <div id="swagger-ui"></div>
                <script src="${swaggerUiBundleUri}"></script>
                <script src="${swaggerUiStandalonePresetUri}"></script>
                <script>
                    window.onload = function() {
                        const spec = ${JSON.stringify(jsonData)};
                        const ui = SwaggerUIBundle({
                            spec: spec,
                            dom_id: '#swagger-ui',
                            deepLinking: true,
                            presets: [
                                SwaggerUIBundle.presets.apis,
                                SwaggerUIBundle.SwaggerUIStandalonePreset
                            ],
                            layout: "BaseLayout",
                            supportedSubmitMethods: ["get", "post", "put", "delete", "patch"],
                            docExpansion: "list",
                            defaultModelsExpandDepth: 1,
                            defaultModelExpandDepth: 1,
                            displayRequestDuration: true,
                            filter: true,
                            showExtensions: true,
                            showCommonExtensions: true,
                            syntaxHighlight: {
                                activated: true,
                                theme: "monokai"
                            },
                            fetch: (url, options) => {
                                return new Promise((resolve, reject) => {
                                    const requestId = Date.now().toString();
                                    
                                    // 从 URL 中提取路径
                                    let path = url;
                                    if (url.startsWith('vscodewebview://')) {
                                        const urlObj = new URL(url);
                                        path = urlObj.pathname;
                                    }
                                    
                                    // 发送请求到 VS Code 扩展
                                    vscode.postMessage({
                                        type: 'apiRequest',
                                        requestId: requestId,
                                        method: options.method,
                                        path: path,
                                        headers: options.headers,
                                        body: options.body,
                                        params: options.params
                                    });

                                    // 监听来自 VS Code 扩展的响应
                                    window.addEventListener('message', function handler(event) {
                                        const message = event.data;
                                        if (message.type === 'apiResponse' && message.requestId === requestId) {
                                            window.removeEventListener('message', handler);
                                            resolve({
                                                ok: true,
                                                status: message.status,
                                                statusText: 'OK',
                                                headers: new Headers(message.headers),
                                                text: () => Promise.resolve(JSON.stringify(message.data)),
                                                json: () => Promise.resolve(message.data)
                                            });
                                        } else if (message.type === 'apiError' && message.requestId === requestId) {
                                            window.removeEventListener('message', handler);
                                            reject(new Error(message.error));
                                        }
                                    });
                                });
                            }
                        });
                    }
                </script>
            </body>
            </html>`;
    } catch (error) {
      console.error(
        "生成 webview 内容失败",
        error instanceof Error ? error : new Error(String(error))
      );
      return `<!DOCTYPE html>
            <html>
            <body>
                <h1>加载 API 文档失败</h1>
                <p>${error instanceof Error ? error.message : String(error)}</p>
            </body>
            </html>`;
    }
  }
}
