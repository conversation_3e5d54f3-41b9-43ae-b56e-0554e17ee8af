@import "tailwindcss";
@import "@radix-ui/themes/styles.css";
@import "@xyflow/react/dist/style.css";

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100%;
  height: 100%;
  padding: 0;
}
html,body {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
}
#root {
  width: 100%;
  height: 100%;
}