import React from 'react';
import CodeEditor from '../../LibComponents/CodeEditor';

interface LongTextRendererProps {
  value?: string;
  onChange?: (value: string) => void;
  label?: string;
  defaultHeight?: number;
}

export const LongTextRenderer: React.FC<LongTextRendererProps> = ({
  value,
  onChange,
  label,
  defaultHeight = 200,
}) => {
  return (
    <div className="long-text-renderer">
      {label && <div className="label">{label}</div>}
      <CodeEditor
        value={value}
        onChange={onChange}
        language="plaintext"
        defaultHeight={defaultHeight}
        resize={true}
      />
    </div>
  );
}; 