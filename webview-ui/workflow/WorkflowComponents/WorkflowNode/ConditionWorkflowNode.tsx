import React, { useEffect, useState } from "react";
import { NodeBasicProps } from "./interface";
import NodeContainer from "./components/NodeContainer";
import { Box, Button, Flex, Select, TextField } from "@radix-ui/themes";
import { PlusIcon, TrashIcon } from "@radix-ui/react-icons";
import { Handle, Position } from "@xyflow/react";
import { createConditionItem } from "./utils";
import { PrevNodeFieldSelector } from "../common/PrevNodeFieldSelector/PrevNodeFieldSelector";
import { useNodeConditionState } from "../WorkflowCanvas";
import {
  WorkflowCondition,
  WorkflowConditionOperatorEnum,
  WorkflowConditionRelationshipEnum,
  WorkflowConditionRelationshipOptions,
  WorkflowConditionOperatorOptions,
} from "@lvjw/code-workflow-types";

export const ConditionWorkflowNode: React.FC<NodeBasicProps> = (props) => {
  const { data } = props;

  const { conditionGroups, updateConditionGroups } = useNodeConditionState(
    props.id
  );


  const { hasCustomHandle } = data;

  const [localConditionGroups, setLocalConditionGroups] = useState<
    WorkflowCondition[]
  >(conditionGroups ?? []);

  useEffect(() => {
    updateConditionGroups(localConditionGroups);
  }, [localConditionGroups]);

  const handleAddConditionGroup = () => {
    setLocalConditionGroups([
      ...localConditionGroups,
      {
        relationship: WorkflowConditionRelationshipEnum.AND,
        conditions: [createConditionItem()],
      },
    ]);
  };

  const handleAddCondition = (groupIndex: number) => {
    const newGroups = [...localConditionGroups];
    newGroups[groupIndex].conditions.push(createConditionItem());
    setLocalConditionGroups(newGroups);
  };

  const handleRemoveCondition = (
    groupIndex: number,
    conditionIndex: number
  ) => {
    const newGroups = [...localConditionGroups];
    newGroups[groupIndex].conditions.splice(conditionIndex, 1);
    if (newGroups[groupIndex].conditions.length === 0) {
      newGroups.splice(groupIndex, 1);
    }
    setLocalConditionGroups(newGroups);
  };

  const handleConditionRelationChange = (groupIndex: number, value: string) => {
    const newGroups = [...localConditionGroups];
    newGroups[groupIndex].relationship =
      value as WorkflowConditionRelationshipEnum;
    setLocalConditionGroups(newGroups);
  };

  const updateConditionLeft = (
    groupIndex: number,
    conditionIndex: number,
    value: {
      label: string;
      labelPath: string[];
      path: string[];
    }
  ) => {
    const newGroups = [...localConditionGroups];
    newGroups[groupIndex].conditions[conditionIndex].left.valuePath =
      value.path;
    newGroups[groupIndex].conditions[conditionIndex].left.valueLabelPath =
      value.labelPath;
    setLocalConditionGroups(newGroups);
  };

  const updateConditionOperator = (
    groupIndex: number,
    conditionIndex: number,
    value: WorkflowConditionOperatorEnum
  ) => {
    const newGroups = [...localConditionGroups];
    newGroups[groupIndex].conditions[conditionIndex].operator = value;
    setLocalConditionGroups(newGroups);
  };

  const updateConditionRight = (
    groupIndex: number,
    conditionIndex: number,
    value: any
  ) => {
    const newGroups = [...localConditionGroups];
    newGroups[groupIndex].conditions[conditionIndex].right.value = value;
    setLocalConditionGroups(newGroups);
  };

  return (
    <NodeContainer
      label={data.label}
      intro="设置条件判断逻辑"
      hasCustomHandle={hasCustomHandle}
    >
      <Handle
        type="target"
        position={Position.Left}
        id="condition-start"
        style={{
          top: "50%",
        }}
      />
      <Box p="3">
        {localConditionGroups.map((group, groupIndex) => (
          <Box
            key={groupIndex}
            mb="4"
            p="3"
            className="border border-gray-200 rounded-md"
            style={{ position: "relative" }}
          >
            <Flex gap="2" mb="2" align="center" gapX="2">
              {/* AND OR 选择 */}
              <Select.Root
                value={group.relationship}
                onValueChange={(value: "AND" | "OR") =>
                  handleConditionRelationChange(groupIndex, value)
                }
              >
                <Select.Trigger />
                <Select.Content>
                  {WorkflowConditionRelationshipOptions.map((option) => (
                    <Select.Item key={option.value} value={option.value}>
                      {option.label}
                    </Select.Item>
                  ))}
                </Select.Content>
              </Select.Root>
              <Button
                variant="ghost"
                onClick={() => handleAddCondition(groupIndex)}
              >
                <PlusIcon />
              </Button>
            </Flex>
            {group.conditions.map((condition, conditionIndex) => (
              <Flex key={conditionIndex} gap="2" mb="2" align="center">
                {/* 左侧 */}
                <PrevNodeFieldSelector
                  currentNodeId={props.id}
                  valuePath={condition.left.valuePath}
                  valueLabelPath={condition.left.valueLabelPath}
                  onChange={(selected) => {
                    updateConditionLeft(groupIndex, conditionIndex, selected);
                  }}
                />

                {/* 符号 */}
                <Select.Root
                  value={condition.operator}
                  onValueChange={(value: WorkflowConditionOperatorEnum) =>
                    updateConditionOperator(groupIndex, conditionIndex, value)
                  }
                >
                  <Select.Trigger />
                  <Select.Content>
                    {WorkflowConditionOperatorOptions.map((option) => (
                      <Select.Item key={option.value} value={option.value}>
                        {option.label}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select.Root>

                {/* 右侧 */}

                <TextField.Root
                  value={condition.right.value}
                  onChange={(e) =>
                    updateConditionRight(
                      groupIndex,
                      conditionIndex,
                      e.target.value
                    )
                  }
                ></TextField.Root>
                <Button
                  variant="ghost"
                  color="red"
                  onClick={() =>
                    handleRemoveCondition(groupIndex, conditionIndex)
                  }
                >
                  <TrashIcon />
                </Button>
              </Flex>
            ))}
            <Handle
              type="source"
              position={Position.Right}
              id={`condition-${groupIndex}`}
              style={{
                top: "50%",
                right: -16,
                transform: "translateY(-50%)",
                background: "#555",
              }}
            />
          </Box>
        ))}
        <Button variant="soft" onClick={handleAddConditionGroup}>
          <PlusIcon /> 添加条件组
        </Button>
      </Box>
    </NodeContainer>
  );
};
function uuidv4(): string {
  throw new Error("Function not implemented.");
}
