import * as vscode from "vscode";
import {
    getVscodeFileContentByURI,
    setVscodeFileContentByURI,
} from "../utils/file";
import path from "path";
import fs from "fs";
import { ConnectionManager } from "../utils/connection/connection-manager";
import OpenAI from "openai";
import { getHtmlForWebview } from "./utils";

export class WorkflowWebviewContainer {
    private panels: Map<string, vscode.WebviewPanel> = new Map();
    private readonly maxPanels: number = 3;
    private connectionManager: ConnectionManager;
    private openai: OpenAI;
    
    private _currentPanel: vscode.WebviewPanel | undefined;

    constructor(private readonly context: vscode.ExtensionContext) {
        this.connectionManager = new ConnectionManager(context);
        this.openai = new OpenAI({
            baseURL: "https://api.deepseek.com",
            apiKey: "***********************************",
        });
    }

    openWorkflowWebviewPanel(basename: string, uri: vscode.Uri) {
        const key = uri.toString();

        // 已有则激活
        if (this.panels.has(key)) {
            this.panels.get(key)!.reveal();
            this._currentPanel = this.panels.get(key);
            return;
        }

        // 超过最大数量，关闭最早的
        if (this.panels.size >= this.maxPanels) {
            const firstKey = this.panels.keys().next().value;
            this.panels.get(firstKey!)?.dispose();
            this.panels.delete(firstKey!);
        }

        // 新建
        const panel = this.createPanel(this.context, basename, uri);
        this.panels.set(key, panel);

        // 关闭时移除
        panel.onDidDispose(() => {
            this.panels.delete(key);
        });
    }

    createPanel(
        context: vscode.ExtensionContext,
        title: string,
        uri: vscode.Uri
    ) {
        const panel = vscode.window.createWebviewPanel(
            "workflowView",
            title,
            vscode.ViewColumn.One,
            {
                // Enable JavaScript in the webview
                enableScripts: true,
                // Restrict the webview to only load resources from the extension's directory
                localResourceRoots: [
                    vscode.Uri.joinPath(
                        context.extensionUri,
                        "dist"
                    ),
                ],
                // Retain the webview when it's not visible
                retainContextWhenHidden: true,
            }
        );

        // 处理来自 webview 的消息
        panel.webview.onDidReceiveMessage(async (message) => {
            switch (message.type) {
                case "getContent":
                    const content = await this.getGraphContent(uri);
                    panel.webview.postMessage({
                        type: "update",
                        content: content,
                    });
                    break;
                case "getWorkflowPath":
                    // 获取工作区根目录
                    const workspaceRoot =
                        vscode.workspace.workspaceFolders?.[0]?.uri.fsPath;
                    if (workspaceRoot) {
                        // 获取当前文件的相对路径
                        const relativePath =
                            vscode.workspace.asRelativePath(uri);
                        panel.webview.postMessage({
                            type: "workflowPath",
                            path: relativePath,
                        });
                    }
                    break;
                case "save":
                    this.saveWorkflow(uri, message.content);

                    break;
                case "error":
                    console.error("Webview error:", message.error);
                    vscode.window.showErrorMessage(
                        "Webview 错误：" + message.error
                    );
                    break;
                case "queryLLMAboutPgSql":
                    try {
                        // 获取当前活动的数据库连接
                        const activeConnection =
                            this.connectionManager.getConnections()[0];
                        if (!activeConnection) {
                            throw new Error("未找到活动的数据库连接");
                        }

                        // 获取数据库结构信息
                        const dbStructure =
                            await this.connectionManager.getDbStructure(
                                activeConnection.id,
                                activeConnection.database || "practice"
                            );
                        console.log(dbStructure);

                        // 使用 OpenAI SDK 调用 API
                        const completion =
                            await this.openai.chat.completions.create({
                                model: "deepseek-chat",
                                messages: [
                                    {
                                        role: "system",
                                        content:
                                            "你是一个专业的 SQL 专家，擅长根据用户需求生成准确的 SQL 查询语句。请只返回 SQL 语句，不要包含其他解释，。",
                                    },
                                    {
                                        role: "user",
                                        content: `基于以下 Postgres SQL 数据库结构：\n${JSON.stringify(
                                            dbStructure,
                                            null,
                                            2
                                        )}\n\n请根据用户的需求生成对应的 Postgres SQL 查询语句：\n${
                                            message.query
                                        }`,
                                    },
                                ],
                            });

                        const generatedSQL =
                            completion.choices[0].message.content?.trim() || "";
                        // 将生成的 SQL 返回给前端
                        panel.webview.postMessage({
                            type: "llmQueryResult",
                            sql: generatedSQL,
                        });
                    } catch (error) {
                        console.error("LLM 查询失败:", error);
                        panel.webview.postMessage({
                            type: "llmQueryError",
                            error:
                                error instanceof Error
                                    ? error.message
                                    : "未知错误",
                        });
                    }
                    break;
            }
        });

        panel.webview.html = getHtmlForWebview(
            vscode.Uri.joinPath(
                context.extensionUri,
                "dist",
                "workflow",
                "index.html"
            ),
            panel.webview,
            context.extensionUri
        );

        return panel;
    }

    async saveWorkflow(uri: vscode.Uri, content: string) {
        try {
            await setVscodeFileContentByURI(uri, content);
            await this.setScriptFiles(content, uri);
            vscode.window.showInformationMessage("保存成功");
        } catch (error) {
            console.error("Save error:", error);
            vscode.window.showErrorMessage("保存失败：" + error);
        }
    }

    async setScriptFiles(content: string, uri: vscode.Uri) {
        var data: any;
        try {
            data = JSON.parse(content);
        } catch (e) {
            return;
        }

        if (!data || typeof data !== "object") {
            console.warn("data is not a valid json");
            return;
        }
        const nodes = data.nodes;
        if (!Array.isArray(nodes) || !nodes.length) {
            console.warn("nodes is not an array or is empty");
            return;
        }

        const scriptNodes = nodes.filter((node: any) => node.type === "code");

        if (scriptNodes.length > 0) {
            const filePath = uri.fsPath;
            const scriptFolder = path.resolve(
                path.dirname(filePath),
                "scripts"
            );
            if (!fs.existsSync(scriptFolder)) {
                fs.mkdirSync(scriptFolder, { recursive: true });
            }
            scriptNodes.forEach((node: any) => {
                const scriptFile = path.resolve(scriptFolder, `${node.id}.js`);
                const codeInput = node.data.inputs.find(
                    (item: any) => item.valueType === "code"
                );
                if (codeInput) {
                    fs.writeFileSync(scriptFile, codeInput.value);
                }
            });
        }
    }

    async getGraphContent(uri: vscode.Uri) {
        const originalContent = await getVscodeFileContentByURI(uri);
        const data = JSON.parse(originalContent);
        if (!data || typeof data !== "object") {
            return originalContent;
        }
        const nodes = data.nodes;
        if (!Array.isArray(nodes) || !nodes.length) {
            return originalContent;
        }

        const filePath = uri.fsPath;
        const scriptFolder = path.resolve(path.dirname(filePath), "scripts");
        const scriptNodes = nodes.filter((node: any) => node.type === "code");

        scriptNodes.forEach(async (node: any) => {
            const scriptFile = path.resolve(scriptFolder, `${node.id}.js`);
            const codeInput = node.data.inputs.find(
                (item: any) => item.valueType === "code"
            );
            if (codeInput) {
                const code = fs.readFileSync(scriptFile, "utf-8");
                codeInput.value = code;
            }
        });

        try {
            return JSON.stringify(data);
        } catch (error) {
            return originalContent;
        }
    }
}