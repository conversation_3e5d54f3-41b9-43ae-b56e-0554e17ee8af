import * as vscode from 'vscode';

/**
 * Logger utility for the extension
 */
export class Logger {
    private static outputChannel: vscode.OutputChannel;

    /**
     * Initialize the logger
     */
    public static initialize(): void {
        if (!Logger.outputChannel) {
            Logger.outputChannel = vscode.window.createOutputChannel('dev expert');
        }
    }

    /**
     * Log an informational message
     */
    public static info(message: string): void {
        Logger.log(`INFO: ${message}`);
    }

    /**
     * Log a debug message
     */
    public static debug(message: string, ...args: any[]): void {
        Logger.log(`DEBUG: ${message}`);
        if (args && args.length > 0) {
            args.forEach(arg => {
                if (typeof arg === 'object') {
                    try {
                        Logger.log(`DEBUG ARGS: ${JSON.stringify(arg)}`);
                    } catch (e) {
                        Logger.log(`DEBUG ARGS: [Object that couldn't be stringified]`);
                    }
                } else {
                    Logger.log(`DEBUG ARGS: ${arg}`);
                }
            });
        }
    }

    /**
     * Log a warning message
     */
    public static warn(message: string): void {
        Logger.log(`WARN: ${message}`);
    }

    /**
     * Log an error message
     */
    public static error(message: string, error?: Error): void {
        Logger.log(`ERROR: ${message}`);

        if (error) {
            Logger.log(`ERROR DETAILS: ${error.message}`);
            if (error.stack) {
                Logger.log(`STACK TRACE: ${error.stack}`);
            }
        }
    }

    /**
     * Log a message to the output channel
     */
    private static log(message: string): void {
        if (!Logger.outputChannel) {
            Logger.initialize();
        }

        const timestamp = new Date().toISOString();
        Logger.outputChannel.appendLine(`[${timestamp}] ${message}`);

        // Also log to console in development mode
        console.log(`[DB-Manager] ${message}`);
    }

    /**
     * Show the output channel
     */
    public static show(): void {
        if (!Logger.outputChannel) {
            Logger.initialize();
        }

        Logger.outputChannel.show();
    }
}
