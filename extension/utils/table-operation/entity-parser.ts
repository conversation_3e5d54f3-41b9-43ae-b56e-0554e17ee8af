import ts from "typescript";
import { DatabaseTableInfo, DatabaseTableColunnInfo } from "./prompt";

function getDecoratorName(decorator: ts.Decorator): string | undefined {
    if (ts.isCallExpression(decorator.expression)) {
        const identifier = decorator.expression.expression;
        if (ts.isIdentifier(identifier)) {
            return identifier.text;
        }
    }
    return undefined;
}

function getDecoratorArguments(decorator: ts.Decorator): ts.NodeArray<ts.Expression> | undefined {
    if (ts.isCallExpression(decorator.expression)) {
        return decorator.expression.arguments;
    }
    return undefined;
}

function getObjectLiteralProperty(obj: ts.ObjectLiteralExpression, propertyName: string): string | undefined {
    const property = obj.properties.find(
        (prop) =>
            ts.isPropertyAssignment(prop) &&
            ts.isIdentifier(prop.name) &&
            prop.name.text === propertyName
    );
    if (
        property &&
        ts.isPropertyAssignment(property) &&
        (ts.isStringLiteral(property.initializer) ||
            ts.isNumericLiteral(property.initializer)) // Allow numeric literals for properties like 'length'
    ) {
        return property.initializer.text;
    }
    return undefined;
}

function extractTypeScriptCode(code: string): string[] {
    const tsBlockRegex = /```(?:typescript|ts)\n([\s\S]*?)```/g; // 添加 g 标志进行全局匹配
    const matches: string[] = [];
    let match;
    while ((match = tsBlockRegex.exec(code)) !== null) {
        matches.push(match[1].trim());
    }
    if (matches.length === 0 && code.trim().length > 0 && !code.includes('```')) {
        return [code.trim()];
    }
    return matches;
}

export function parseEntityToSchema(markdownContent: string): DatabaseTableInfo[] {
    const entityCodeBlocks = extractTypeScriptCode(markdownContent);
    const schemas: DatabaseTableInfo[] = [];

    for (const entityCode of entityCodeBlocks) {
        const sourceFile = ts.createSourceFile(
            "temp.ts", // 文件名在这里不重要，因为我们直接处理代码字符串
            entityCode,
            ts.ScriptTarget.Latest,
            true
        );

        let currentSchema: Partial<DatabaseTableInfo> = {};
        const columns: DatabaseTableColunnInfo[] = [];

        ts.forEachChild(sourceFile, (node) => {
            if (ts.isClassDeclaration(node) && node.name) {
                const decorators = ts.getDecorators(node);
                const entityDecorator = decorators?.find(
                    (d) => getDecoratorName(d) === "Entity"
                );

                if (entityDecorator) {
                    currentSchema.name = node.name.text;
                    const args = getDecoratorArguments(entityDecorator);
                    if (args && args.length > 0 && ts.isObjectLiteralExpression(args[0])) {
                        const entityOptions = args[0] as ts.ObjectLiteralExpression;
                        currentSchema.name =
                            getObjectLiteralProperty(entityOptions, "name") || currentSchema.name;
                        currentSchema.comment =
                            getObjectLiteralProperty(entityOptions, "comment") || "";
                    }
                }

                node.members.forEach((member) => {
                    if (
                        ts.isPropertyDeclaration(member) &&
                        member.name &&
                        ts.isIdentifier(member.name)
                    ) {
                        const propDecorators = ts.getDecorators(member);
                        const columnDecorator = propDecorators?.find(
                            (d) => getDecoratorName(d) === "Column" || getDecoratorName(d) === "ManyToOne" || getDecoratorName(d) === "OneToMany"
                        );
                        const primaryColumnDecorator = propDecorators?.find(
                            (d) => getDecoratorName(d) === "PrimaryGeneratedColumn" || getDecoratorName(d) === "PrimaryColumn"
                        );

                        let isPrimaryKey = false;
                        let decoratorToParse = columnDecorator;

                        if (primaryColumnDecorator) {
                            isPrimaryKey = true;
                            decoratorToParse = primaryColumnDecorator; // Prefer primary column decorator for comment
                        }
                        
                        if (decoratorToParse) {
                            const columnName = member.name.text;
                            let columnComment = "";
                            // TODO: 提取列类型, 关系等
                            let columnType = ''; // Placeholder for type extraction
                            if (member.type && ts.isTypeNode(member.type)) {
                                columnType = member.type.getText(sourceFile);
                            }

                            const colArgs = getDecoratorArguments(decoratorToParse);
                            if (colArgs && colArgs.length > 0 && ts.isObjectLiteralExpression(colArgs[0])) {
                                const columnOptions = colArgs[0] as ts.ObjectLiteralExpression;
                                columnComment =
                                    getObjectLiteralProperty(columnOptions, "comment") || "";
                            }

                            columns.push({
                                name: columnName,
                                comment: columnComment,
                                isPrimaryKey: isPrimaryKey, // 如果 DatabaseTableColunnInfo 需要此字段
                                type: columnType 
                            });
                        }
                    }
                });
                currentSchema.columns = columns;
                if (currentSchema.name && currentSchema.columns) {
                     schemas.push(currentSchema as DatabaseTableInfo);
                }
                currentSchema = {}; // Reset for next class in the same block (if any, though unlikely for typical entity files)
            }
        });
    }
    return schemas;
}