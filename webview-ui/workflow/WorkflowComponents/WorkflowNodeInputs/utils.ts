import { v4 as uuidv4 } from "uuid";
import { WorkflowIOValueTypeEnum, WorkflowNodeInputField } from "@lvjw/code-workflow-types";

export const createNewNodeInputField = (
    nodeId: string
  ): WorkflowNodeInputField => {
    return {
      id: uuidv4().replaceAll("-", ""),
      attrName: "newInput",
      value: null,
      valuePath: [],
      valueLabelPath: [],
      isDynamic: true,
      valueType: WorkflowIOValueTypeEnum.string,
      parentId: nodeId,
      editable: true,
      removable: true,
    } as any;
  };
  
  