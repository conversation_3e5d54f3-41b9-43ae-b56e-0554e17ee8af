import React from "react";  
import { WorkflowNodeInputsProps } from "./interface";
import { useNodeInputsState } from "../WorkflowCanvas";
import { WorkflowNodeInputFieldComp } from "./WorkflowNodeInputFieldComp";
import { Flex } from "@radix-ui/themes";

const WorkflowNodeInputs:React.FC<WorkflowNodeInputsProps> = ({inputs, node}) => {
    // const {inputs} = useNodeInputsState(node.id)

    return (
        <Flex direction="column" gap="2">
            {inputs.map((input) => (
                <WorkflowNodeInputFieldComp key={input.id} input={input} node={node} />
            ))}   
        </Flex>
    )
}

export const WorkflowNodeInputsMemo = React.memo(WorkflowNodeInputs)


