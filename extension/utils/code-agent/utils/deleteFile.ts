import fs from 'fs/promises'
import { Logger } from './logger'

export async function deleteFile(targetFile: string,output?: (response: string) => void,
  logger?: Logger): Promise<[string, boolean]> {
  try {
    try {
      await fs.access(targetFile)
    } catch {
      return [`File ${targetFile} does not exist`, false]
    }

    await fs.unlink(targetFile)
    return [`Successfully deleted ${targetFile}`, true]
  } catch (error) {
    return [`Error deleting file: ${error}`, false]
  }
}