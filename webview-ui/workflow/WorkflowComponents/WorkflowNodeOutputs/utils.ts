import { v4 as uuidv4 } from "uuid";
import { WorkflowIOValueTypeEnum , WorkflowNodeOuputField} from "@lvjw/code-workflow-types";

export const createNewNodeOutputField = (
  nodeId: string
): WorkflowNodeOuputField => {
  return {
    id: uuidv4().replaceAll("-", ""),
    attrName: "newOutput",
    valueType: WorkflowIOValueTypeEnum.string,
    value: null,
    valuePath: [],
    valueLabelPath: [],
    isDynamic: true,
    parentId: nodeId,
    editable: true,
    removable: true,
  } as any;
};

