import * as vscode from "vscode";
import * as fs from "fs";
import { getNonce } from "../utils/nonce"; // 假设 nonce 工具函数位于上一级的 utils 目录

export function getHtmlForWebview(
  htmlUri: vscode.Uri,
  webview: vscode.Webview,
  extensionUri: vscode.Uri
): string {
  const html = fs.readFileSync(htmlUri.fsPath, "utf-8");
  const nonce = getNonce();
  
  // 替换代码中的 script 的 src 和 css 的 href 的路径前缀，使其在 webview 中正确加载
  let modifiedHtml = html;
  
  // 替换CSS链接路径
  modifiedHtml = modifiedHtml.replace(
    /href="\/([^"]+\.css)"/g,
    (match, relativePath) => {
      const cssUri = webview.asWebviewUri(
        vscode.Uri.joinPath(extensionUri, "dist", relativePath)
      );
      return `href="${cssUri}"`;
    }
  );
  
  // 替换JavaScript脚本路径
  modifiedHtml = modifiedHtml.replace(
    /src="\/([^"]+\.js)"/g,
    (match, relativePath) => {
      const jsUri = webview.asWebviewUri(
        vscode.Uri.joinPath(extensionUri, "dist", relativePath)
      );
      return `src="${jsUri}"`;
    }
  );
  
  // 替换modulepreload链接路径
  modifiedHtml = modifiedHtml.replace(
    /href="\/([^"]+\.js)"/g,
    (match, relativePath) => {
      const jsUri = webview.asWebviewUri(
        vscode.Uri.joinPath(extensionUri, "dist", relativePath)
      );
      return `href="${jsUri}"`;
    }
  );
  
  // 为所有script和link标签添加nonce属性
  modifiedHtml = modifiedHtml.replace(
    /<script([^>]*)>/g,
    `<script nonce="${nonce}"$1>`
  );
  
  modifiedHtml = modifiedHtml.replace(
    /<link([^>]*rel="stylesheet"[^>]*)>/g,
    `<link nonce="${nonce}"$1>`
  );
  
  // 在 </head> 标签前添加 VSCode API 脚本
  const vscodeScript = `
    <script nonce="${nonce}">
      window.vscode = acquireVsCodeApi();
    </script>`;
  
  modifiedHtml = modifiedHtml.replace(
    /<\/head>/,
    `${vscodeScript}
  </head>`
  );
  
  return modifiedHtml;
}