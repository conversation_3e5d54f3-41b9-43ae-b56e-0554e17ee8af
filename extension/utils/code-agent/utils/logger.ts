import path from "path";
import fs from "fs";
import * as vscode from "vscode";

const logDirectory = process.env.LOG_DIR || "logs";

export class Logger {
  private logFile: string;

  constructor(context: vscode.ExtensionContext) {
    const date = new Date().toISOString().split("T")[0].replace(/-/g, "");
    const workspaceFolders = vscode.workspace.workspaceFolders;
    this.logFile = path.join( workspaceFolders?.[0]?.uri?.fsPath ?? '', logDirectory, `llm_calls_${date}.log`);
    this.ensureLogDirectory();
  }

  private async ensureLogDirectory() {
    try {
      await fs.promises.mkdir(logDirectory, { recursive: true });
    } catch (error) {
      // Directory might already exist
    }
  }

  async info(message: string) {
    const timestamp = new Date().toISOString();
    const logEntry = `${timestamp} - INFO - ${message}\n`;
    try {
      fs.promises.appendFile(this.logFile, logEntry);
    } catch (error) {
      console.error("Failed to write to log file:", error);
    }
  }

  async warning(message: string) {
    const timestamp = new Date().toISOString();
    const logEntry = `${timestamp} - WARNING - ${message}\n`;
    try {
      fs.promises.appendFile(this.logFile, logEntry);
    } catch (error) {
      console.error("Failed to write to log file:", error);
    }
  }

  async error(message: string) {
    const timestamp = new Date().toISOString();
    const logEntry = `${timestamp} - ERROR - ${message}\n`;
    try {
      fs.promises.appendFile(this.logFile, logEntry);
    } catch (error) {
      console.error("Failed to write to log file:", error);
    }
  }
}
