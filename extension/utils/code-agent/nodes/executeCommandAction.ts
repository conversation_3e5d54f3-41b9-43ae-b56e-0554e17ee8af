import { Node } from 'pocketflow'
import { CodeAgentActionEnum, CodingAgentSharedStore, History, Output } from '../types'
import * as vscode from 'vscode'

interface PrepResult {
    command: string
    requiresApproval: boolean
    output: Output
    workingDir: string
}

export class ExecuteCommandActionNode extends Node<CodingAgentSharedStore> {
    async prep(shared: CodingAgentSharedStore): Promise<PrepResult> {
        const lastAction = shared.currentAction!
        const command = lastAction.params.command
        const requiresApproval = lastAction.params.requiresApproval

        return {
            command,
            requiresApproval,
            output: shared.output,
            workingDir: shared.workingDir,
        }
    }

    async exec(prepResult: PrepResult): Promise<{ output: string; success: boolean; exitCode?: number }> {
        prepResult.output.text(`ExecuteCommand - exec: 执行命令 ${prepResult.command}`)

        try {
            // 如果需要用户批准，先询问用户
            if (prepResult.requiresApproval) {
                const result = await vscode.window.showWarningMessage(
                    `即将执行命令: ${prepResult.command}`,
                    { modal: true },
                    '执行',
                    '取消'
                )
                
                if (result !== '执行') {
                    prepResult.output.text('ExecuteCommand - exec: 用户取消执行命令')
                    return { output: '用户取消执行命令', success: false }
                }
            }

            // 创建终端并执行命令
            const terminal = vscode.window.createTerminal({
                name: 'Code Agent Command',
                cwd: prepResult.workingDir
            })

            // 显示终端
            terminal.show()

            // 发送命令到终端
            terminal.sendText(prepResult.command)

            prepResult.output.text(`ExecuteCommand - exec: 命令已发送到终端: ${prepResult.command}`)

            // 注意：VSCode终端API不提供直接获取命令输出的方法
            // 这里我们返回成功状态，实际输出用户可以在终端中查看
            return {
                output: `命令 '${prepResult.command}' 已在终端中执行，请查看终端输出`,
                success: true
            }

        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error)
            prepResult.output.text(`ExecuteCommand - exec: 执行命令失败: ${errorMessage}`)
            return {
                output: `执行命令失败: ${errorMessage}`,
                success: false
            }
        }
    }

    async post(
        shared: CodingAgentSharedStore,
        prepResult: PrepResult,
        execResult: { output: string; success: boolean; exitCode?: number },
    ): Promise<CodeAgentActionEnum> {
        shared.history.push({
            role: 'user',
            content: JSON.stringify({
                ...shared.currentAction!,
                result: execResult.output,
                success: execResult.success,
            }),
        })

        shared.currentAction = null

        return CodeAgentActionEnum.default
    }
}