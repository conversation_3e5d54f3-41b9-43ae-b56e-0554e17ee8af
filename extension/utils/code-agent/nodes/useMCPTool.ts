import { Node } from 'pocketflow'
import { CodeAgentActionEnum, CodingAgentSharedStore, History, Output } from '../types'
import { McpHub } from '../../../services/mcp/McpHub'
import { McpToolCallResponse } from '../../../services/mcp/types'

interface PrepResult {
    mcpHub?: McpHub
    serverName: string
    toolName: string
    arguments: Record<string, any>
    output: Output
}

export class UserMCPToolNode extends Node<CodingAgentSharedStore> {
    async prep(shared: CodingAgentSharedStore): Promise<PrepResult> {
        const { serverName, toolName, arguments: args } = shared.currentAction!.params

        return {
            mcpHub: shared.mcpHub,
            serverName,
            toolName,
            arguments: args,
            output: shared.output,
        }
    }

    async exec(prepResult: PrepResult): Promise<McpToolCallResponse> {
        const { serverName, toolName, arguments: args, mcpHub } = prepResult

        if (!mcpHub) {
            throw new Error('McpHub not connected')
        }
        if (!serverName) {
            throw new Error('serverName is required')
        }

        if (!toolName) {
            throw new Error('toolName is required')
        }

        if (!args) {
            throw new Error('arguments is required')
        }
        const result = await mcpHub.callTool(serverName, toolName, args)
        await prepResult.output.text(
            `callMCPTool - exec: 调用 MCP 工具 ${toolName} 成功, 返回结果:\n` +
                JSON.stringify(result),
        )
        return result
    }

    async post(
        shared: CodingAgentSharedStore,
        prepResult: PrepResult,
        execResult: McpToolCallResponse,
    ): Promise<CodeAgentActionEnum> {
        shared.history.push({
            role: 'user',
            content: JSON.stringify({
                ...shared.currentAction!,
                result: execResult,
                success: !execResult.isError,
            }),
        })

        shared.currentAction = null

        return CodeAgentActionEnum.default
    }
}
