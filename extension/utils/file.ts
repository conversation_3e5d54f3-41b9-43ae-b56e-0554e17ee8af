import * as vscode from "vscode";

export async function getVscodeFileContentByURI(uri: vscode.Uri) {
  try {
    // 打开文本文档
    const document = await vscode.workspace.openTextDocument(uri);
    // 获取文件内容
    const content = document.getText();
    return content;
  } catch (error: any) {
    throw new Error(`Failed to read file: ${error.message}`);
  }
}

export async function setVscodeFileContentByURI(
  uri: vscode.Uri,
  content: string
) {
    try {
        // 打开文本文档
        const document = await vscode.workspace.openTextDocument(uri);
        // 创建一个编辑操作，将整个文件内容替换为新内容
        const edit = new vscode.WorkspaceEdit();
        const fullRange = new vscode.Range(
          document.positionAt(0),
          document.positionAt(document.getText().length)
        );
        edit.replace(uri, fullRange, content);
        // 应用编辑
        await vscode.workspace.applyEdit(edit);
        // 保存文件
        await document.save();
      } catch (error: any) {
        throw new Error(`Failed to write file: ${error.message}`);
      }
}
