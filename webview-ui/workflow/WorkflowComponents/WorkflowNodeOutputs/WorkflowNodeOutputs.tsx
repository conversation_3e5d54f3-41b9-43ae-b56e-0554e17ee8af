import React from "react";  
import { WorkflowNodeOutputsProps } from "./interface";
import { Flex } from "@radix-ui/themes";
import { WorkflowNodeOutputFieldComp } from "./WorkflowNodeOutputFieldComp";

const WorkflowNodeOutputs:React.FC<WorkflowNodeOutputsProps> = ({outputs, node}) => {
    return (
        <Flex direction="column" gap="2">
            {outputs.map((output) => (
                <WorkflowNodeOutputFieldComp key={output.id} output={output} node={node} />
            ))}   
        </Flex>
    )
}

export const WorkflowNodeOutputsMemo = React.memo(WorkflowNodeOutputs)


