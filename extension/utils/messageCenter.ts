import { Remitter } from 'remitter';

export enum MessageEvent {
  USER_MESSAGE_RECEIVED = 'USER_MESSAGE_RECEIVED',
  NEW_CHAT_REQUESTED = 'NEW_CHAT_REQUESTED',
  // Add other event types as needed
}

class Message<PERSON>enter extends Remitter {
  private static instance: MessageCenter;

  private constructor() {
    super();
  }

  public static getInstance(): MessageCenter {
    if (!MessageCenter.instance) {
      MessageCenter.instance = new MessageCenter();
    }
    return MessageCenter.instance;
  }

  public sendMessage(event: MessageEvent, data?: any): void {
    this.emit(event, data);
  }

  public onMessage(event: MessageEvent, listener: (...args: any[]) => void): () => void {
    this.on(event, listener);
    return () => this.off(event, listener); // Return a function to unsubscribe
  }
}

export const messageCenter = MessageCenter.getInstance();