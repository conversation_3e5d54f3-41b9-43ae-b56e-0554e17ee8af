const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const packageJsonPath = path.resolve(__dirname, '..', 'package.json');

function updateVersion() {
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
    const currentVersion = packageJson.version;
    const versionParts = currentVersion.split('.');
    versionParts[2] = parseInt(versionParts[2], 10) + 1;
    const newVersion = versionParts.join('.');
    packageJson.version = newVersion;
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
    console.log(`Version updated from ${currentVersion} to ${newVersion}`);
    return newVersion;
  } catch (error) {
    console.error('Failed to update version:', error);
    process.exit(1);
  }
}

function packageExtension() {
  try {
    console.log('Packaging extension...');
    execSync('npx vsce package', { stdio: 'inherit' });
    console.log('Extension packaged successfully.');
  } catch (error) {
    console.error('Failed to package extension:', error);
    process.exit(1);
  }
}

function commitChanges(version) {
  try {
    console.log('Committing changes...');
    execSync('git add .', { stdio: 'inherit' });
    execSync(`git commit -m "chore: package ${version}"`, { stdio: 'inherit' });
    console.log('Changes committed successfully.');
  } catch (error) {
    console.error('Failed to commit changes:', error);
    // Not exiting with 1 here, as packaging might still be considered successful
    // depending on workflow. User can decide if this is a critical failure.
    console.warn('Warning: Failed to commit changes. Please do it manually.');
  }
}

function main() {
  const newVersion = updateVersion();
  packageExtension();
  commitChanges(newVersion);
  console.log(`Successfully packaged version ${newVersion}`);
}

main();