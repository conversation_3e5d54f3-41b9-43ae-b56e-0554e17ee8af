import { Node } from 'pocketflow'
import { deleteFile } from '../utils/deleteFile'
import { CodeAgentActionEnum, CodingAgentSharedStore, History, Output } from '../types'
import path from 'path'

interface PrepResult {
    filePath: string
    history: History[]
    output: Output
}

export class DeleteFileActionNode extends Node<CodingAgentSharedStore> {
    async prep(shared: CodingAgentSharedStore): Promise<PrepResult> {
        const targetFile = shared.currentAction!.params.targetFile
        const filePath = path.resolve(shared.workingDir, targetFile)

        shared.output.text(`DeleteFileAction - prep: 文件路径: ${filePath}`)

        return { filePath, history: shared.history, output: shared.output }
    }

    async exec(prepResult: PrepResult): Promise<{ message: string; success: boolean }> {
        prepResult.output.text(`DeleteFileAction - exec: 删除文件 ${prepResult.filePath}`)

        const [message, success] = await deleteFile(prepResult.filePath)

        prepResult.output.text(
            `DeleteFileAction - exec: 删除${success ? '成功' : '失败'}: ${message}`,
        )

        return { message, success }
    }

    async post(
        shared: CodingAgentSharedStore,
        prepResult: PrepResult,
        execResult: { message: string; success: boolean },
    ) {
        prepResult.output.text(
            execResult.success ? '文件删除成功' : `文件删除失败: ${execResult.message}`,
        )

        shared.history.push({
            role: 'user',
            content: JSON.stringify({
                ...shared.currentAction,
                result: execResult.message,
                success: execResult.success,
            }),
        })

        shared.currentAction = null

        return CodeAgentActionEnum.default
    }
}
