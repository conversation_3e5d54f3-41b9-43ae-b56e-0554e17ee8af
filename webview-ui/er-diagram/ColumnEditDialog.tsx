import React, { useEffect, useState } from 'react';
import { <PERSON>alog, Button, Flex, Text, TextField, Checkbox } from '@radix-ui/themes';
import type { Column } from './interface';

interface ColumnEditDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  columnData?: Column | null; // Nullable to represent new column explicitly
  onSave: (column: Column) => void;
  // We might need an index if we are editing an existing column directly in an array
  // For now, onSave will handle adding or updating logic in the parent
}

const DEFAULT_COLUMN: Column = {
  name: '',
  type: 'VARCHAR',
  comment: '',
  isPrimaryKey: false,
  isNullable: true, // Assuming default, adjust as per your Column interface
  // Add other default fields from your Column interface if any
};

export function ColumnEditDialog({ isOpen, onOpenChange, columnData, onSave }: ColumnEditDialogProps) {
  const [name, setName] = useState('');
  const [type, setType] = useState('VARCHAR');
  const [comment, setComment] = useState('');
  const [isPrimaryKey, setIsPrimaryKey] = useState(false);
  // Add states for other editable column properties from your Column interface

  useEffect(() => {
    if (isOpen && columnData) {
      setName(columnData.name);
      setType(columnData.type);
      setComment(columnData.comment || '');
      setIsPrimaryKey(columnData.isPrimaryKey);
      // Set other states from columnData
    } else if (isOpen && !columnData) {
      // Reset to default for new column
      setName(DEFAULT_COLUMN.name);
      setType(DEFAULT_COLUMN.type);
      setComment(DEFAULT_COLUMN.comment);
      setIsPrimaryKey(DEFAULT_COLUMN.isPrimaryKey);
      // Reset other states to default
    }
  }, [isOpen, columnData]);

  const handleSave = () => {
    const savedColumn: Column = {
      // If columnData exists, it means we are editing, so we can spread its existing properties
      // to preserve any non-editable fields or fields not yet in the form.
      ...(columnData || DEFAULT_COLUMN), // Base with existing or default
      name,
      type,
      comment,
      isPrimaryKey,
      // Add other properties to save
    };
    onSave(savedColumn);
    onOpenChange(false); // Close dialog on save
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog.Root open={isOpen} onOpenChange={onOpenChange}>
      <Dialog.Content style={{ maxWidth: 450 }}>
        <Dialog.Title>{columnData ? '编辑列' : '添加新列'}</Dialog.Title>
        <Dialog.Description size="2" mb="4">
          {columnData ? '修改列的详细信息。' : '输入新列的详细信息。'}
        </Dialog.Description>

        <Flex direction="column" gap="3">
          <label>
            <Text as="div" size="2" mb="1" weight="bold">
              列名
            </Text>
            <TextField.Root
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="输入列名"
            />
          </label>
          <label>
            <Text as="div" size="2" mb="1" weight="bold">
              类型
            </Text>
            <TextField.Root
              value={type}
              onChange={(e) => setType(e.target.value)}
              placeholder="例如: VARCHAR, INT, TEXT"
            />
          </label>
          <label>
            <Text as="div" size="2" mb="1" weight="bold">
              注释
            </Text>
            <TextField.Root // Changed from TextArea for consistency, can be TextArea if preferred
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="输入列注释"
            />
          </label>
          <Text as="label" size="2">
            <Flex gap="2" align="center">
              <Checkbox 
                checked={isPrimaryKey}
                onCheckedChange={(checked) => setIsPrimaryKey(checked as boolean)}
              /> 
              是否主键
            </Flex>
          </Text>
          {/* Add other input fields for column properties here */}
        </Flex>

        <Flex gap="3" mt="4" justify="end">
          <Button variant="soft" color="gray" onClick={handleCancel}>
            取消
          </Button>
          <Button onClick={handleSave}>保存</Button>
        </Flex>
      </Dialog.Content>
    </Dialog.Root>
  );
}