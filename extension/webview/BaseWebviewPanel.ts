import * as vscode from "vscode";
import { getHtmlForWebview } from "./utils";
import { MessageFromWebview } from "./interface";

interface BaseWebviewPanelConfig {
    context: vscode.ExtensionContext;
    type: string;
    distHtmlPath: vscode.Uri;
    title: string;
    maxCount?: number;
}

export class BaseWebviewPanelContainer {
    private _context: vscode.ExtensionContext;
    private _distHtmlPath?: vscode.Uri | null;
    private _panels: Map<string, vscode.WebviewPanel> = new Map();
    private _maxCount: number = 1;
    private _type: string;
    private _title: string;

    constructor(config: BaseWebviewPanelConfig) {
        let { context, type, distHtmlPath, title, maxCount } = config;
        maxCount ??= 1;
        this._context = context;
        this._distHtmlPath = distHtmlPath;
        this._type = type;
        this._title = title;
        this._maxCount = maxCount;
    }

    _show(key?: string) {
        // 已有则激活
        if (key && this._panels.has(key)) {
            this._panels.get(key)!.reveal();
            return;
        }

        // 超过最大数量，关闭最早的
        if (this._panels.size >= this._maxCount) {
            const firstKey = this._panels.keys().next().value;
            this._panels.get(firstKey!)?.dispose();
            this._panels.delete(firstKey!);
        }

        key ??= this._type;
        // 新建
        const panel = this._createPanel();
        this._panels.set(key, panel);
        console.log('+++++++', key, panel.webview.html)

        // 关闭时移除
        panel.onDidDispose(() => {
            this._panels.delete(key);
        });
    }

    _createPanel(): vscode.WebviewPanel {
        const panel = vscode.window.createWebviewPanel(
            this._type,
            this._title,
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(this._context.extensionUri, "dist"),
                ],
            }
        );

        panel.webview.html = getHtmlForWebview(
            this._distHtmlPath!,
            panel.webview,
            this._context.extensionUri
        );

        console.log('13123', getHtmlForWebview(
            this._distHtmlPath!,
            panel.webview,
            this._context.extensionUri
        ))

        panel.webview.onDidReceiveMessage(
            async (message: MessageFromWebview) => {
                this.handleMessage(message, panel);
            }
        );
        return panel;
    }

    async handleMessage(message: MessageFromWebview, panel: vscode.WebviewPanel) {
        console.log("message", message);
        console.log("请在子类型 webview 中实现 handleMessage 方法");
    }

    updateTitle(title: string) {
        this._title = title;
    }
}
