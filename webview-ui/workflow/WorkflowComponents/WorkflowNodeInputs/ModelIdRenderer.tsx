import React from 'react';
import Cascader from '../../LibComponents/Cascader';

interface ModelIdValue {
  value: string;
  labelPath: string[];
  valuePath: string[];
}

interface ModelIdRendererProps {
  value?: ModelIdValue;
  onChange?: (value: ModelIdValue) => void;
  label?: string;
}

const modelOptions = [
  {
    value: 'deepseek',
    label: 'DeepSeek',
    children: [
      {
        value: 'deepseek-chat',
        label: 'DeepSeek Chat',
      },
      {
        value: 'deepseek-reasoning',
        label: 'DeepSeek Reasoning',
      },
    ],
  },
];

export const ModelIdRenderer: React.FC<ModelIdRendererProps> = ({
  value,
  onChange,
  label = '选择模型',
}) => {
  const handleModelChange = (selected: { path: string[]; label: string; labelPath: string[] }) => {
    if (onChange) {
      onChange({
        value: selected.path[selected.path.length - 1],
        labelPath: selected.labelPath,
        valuePath: selected.path,
      });
    }
  };

  return (
    <div className="model-id-renderer">
      <Cascader
        data={modelOptions}
        triggerLabel={label}
        onChange={handleModelChange}
        defaultSelectedPath={value?.valuePath}
        defaultSelectedLabelPath={value?.labelPath}
      />
    </div>
  );
}; 