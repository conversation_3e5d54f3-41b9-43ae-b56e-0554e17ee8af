import { useMemo } from 'react';
import { useAtom } from 'jotai';
import { nodesAtom, edgesAtom } from '../../WorkflowCanvas';
import { buildGraph, getPreviousNodes, buildCascaderData } from '../../WorkflowNodeInputs/helpers';
import { CascaderItem } from '../../../LibComponents/Cascader';

export const usePrevNodeFieldSelector = (currentNodeId: string) => {
  // 从 jotai 获取数据
  const [nodes] = useAtom(nodesAtom);
  const [edges] = useAtom(edgesAtom);

  // 构建图并获取前置节点
  const graph = useMemo(() => buildGraph(nodes, edges), [nodes, edges]);
  const previousNodeIds = useMemo(() => getPreviousNodes(graph, currentNodeId), [graph, currentNodeId]);
  
  // 构建 Cascader 数据
  const cascaderData = useMemo(() => 
    buildCascaderData(nodes, previousNodeIds), 
    [nodes, previousNodeIds]
  );

  return {
    cascaderData
  };
}; 