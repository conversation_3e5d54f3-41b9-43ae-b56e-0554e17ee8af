// 定义 Cascader 单个项目的类型
export interface CascaderItem {
  value: string;
  label: string;
  children?: CascaderItem[];
  disabled?: boolean; // 可选项，用于禁用某个菜单项
}

// 定义 BasicCascader 组件的 props 类型
export interface BasicCascaderProps {
  data: CascaderItem[];
  triggerLabel?: string;
  onChange?: (selected: { path: string[]; label: string; labelPath: string[] }) => void; // 添加 labelPath
  defaultSelectedPath?: string[];
  defaultSelectedLabelPath?: string[];
}

// 定义 renderMenuItems 函数的参数类型
export type RenderMenuItemsFn = (
  items: CascaderItem[],
  currentPath: string[],
  currentLabelPath: string[], // 新增: 当前标签路径
  onSelect: (path: string[], label: string, labelPath: string[]) => void, // onSelect 现在也需要 labelPath
  selectedValuePath: string
) => React.ReactNode[];