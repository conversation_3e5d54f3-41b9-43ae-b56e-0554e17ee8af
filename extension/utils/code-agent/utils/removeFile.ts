import fs from 'fs/promises'
import { Logger } from './logger'

export async function removeFile(
  targetFile: string,
  startLine?: number,
  endLine?: number,
  output?: (response: string) => void,
  logger?: Logger
): Promise<[string, boolean]> {
  try {
    // Check if file exists
    try {
      await fs.access(targetFile)
    } catch {
      return [`Error: File ${targetFile} does not exist`, false]
    }

    // Require at least one of start_line or end_line to be specified
    if (startLine === undefined && endLine === undefined) {
      return ['Error: At least one of startLine or endLine must be specified', false]
    }

    // Read the file content
    const content = await fs.readFile(targetFile, 'utf-8')
    const lines = content.split('\n')

    // Validate line numbers
    if (startLine !== undefined && startLine < 1) {
      return ['Error: startLine must be at least 1', false]
    }

    if (endLine !== undefined && endLine < 1) {
      return ['Error: endLine must be at least 1', false]
    }

    if (startLine !== undefined && endLine !== undefined && startLine > endLine) {
      return ['Error: startLine must be less than or equal to endLine', false]
    }

    // Adjust for 1-indexed to 0-indexed
    const startIdx = startLine !== undefined ? startLine - 1 : 0
    const endIdx = endLine !== undefined ? endLine - 1 : lines.length - 1

    // Don't report error if start_line is beyond file length
    if (startIdx >= lines.length) {
      return [`No lines removed: startLine (${startLine}) exceeds file length (${lines.length})`, true]
    }

    // Remove the specified lines
    lines.splice(startIdx, endIdx - startIdx + 1)

    // Write the updated content back to the file
    await fs.writeFile(targetFile, lines.join('\n'), 'utf-8')

    // Prepare message based on what was removed
    let message: string
    if (startLine === undefined) {
      message = `Successfully removed lines 1 to ${endLine} from ${targetFile}`
    } else if (endLine === undefined) {
      message = `Successfully removed lines ${startLine} to end from ${targetFile}`
    } else {
      message = `Successfully removed lines ${startLine} to ${endLine} from ${targetFile}`
    }

    return [message, true]
  } catch (error) {
    return [`Error removing content: ${error}`, false]
  }
}
