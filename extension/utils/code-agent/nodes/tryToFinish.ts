import { Node } from 'pocketflow'
import { callLlm } from '../utils/callLlm'
import { CodeAgentActionEnum, CodingAgentSharedStore, History, NodeResult, Output } from '../types';
import { messageCenter, MessageEvent } from '../../messageCenter';

interface PrepResult {
    history: History[]
    output: Output
}

export class TryToFinishNode extends Node<CodingAgentSharedStore> {
    async prep(shared: CodingAgentSharedStore): Promise<PrepResult> {
        return {
            history: shared.history,
            output: shared.output,
        }
    }

    async exec(prepResult: PrepResult): Promise<{ success: boolean; content: string }> {
        // TODO LJW
        const prompt = `
你是一个编程助手。根据执行历史记录，为用户生成一个清晰、有用的响应。

执行历史:
${JSON.stringify(prepResult.history, null, 2)}

请生成一个友好、专业的响应，总结执行的操作和结果。如果有错误，请说明问题所在。

要求：
只回复相应内容即可，不要携带任何其他内容
`

        try {
            const response = await callLlm([
                {
                    role: 'user',
                    content: prompt,
                },
            ])
            prepResult.output.text(JSON.stringify(response))

            return { success: true, content: response }
        } catch (e) {
            return {
                success: false,
                content: e instanceof Error ? e.message : '生成总结失败',
            }
        }
    }

    async post(
        shared: CodingAgentSharedStore,
        prepResult: PrepResult,
        execResult: { success: boolean; content: string },
    ): Promise<string> {
        shared.history.push({
            role: 'user',
            content: JSON.stringify({
                ...shared.currentAction,
                result: execResult.content,
                success: execResult.success,
            }),
        })

        return new Promise(resolve => {
            const unsubscribeUserMessage = messageCenter.onMessage(MessageEvent.USER_MESSAGE_RECEIVED, (message: string) => {
                shared.history.push({
                    role: 'user',
                    content: message,
                })
                unsubscribeUserMessage(); // Unsubscribe after handling
                unsubscribeNewChat(); // Unsubscribe other listener as well
                resolve(CodeAgentActionEnum.userQuery);
            });

            const unsubscribeNewChat = messageCenter.onMessage(MessageEvent.NEW_CHAT_REQUESTED, () => {
                // TODO:  这里可能需要一种方式来通知 Flow 重置或结束
                // 目前，我们简单地解析为默认动作，这可能需要根据实际流程需求调整
                console.log('New chat requested during tryToFinish node');
                unsubscribeUserMessage(); // Unsubscribe other listener
                unsubscribeNewChat(); // Unsubscribe after handling
                resolve(CodeAgentActionEnum.default);
            });

            // It's important to have a way to resolve the promise if no message is received,
            // otherwise the flow might hang. This could be a timeout or a default action.
            // For now, we'll rely on the webview to send a message or new chat request.
            // If nothing happens, this promise will not resolve, which might be an issue.
            // Consider adding a timeout mechanism here if needed.
        });
    }
}
