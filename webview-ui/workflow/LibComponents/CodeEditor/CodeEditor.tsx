import React, { useCallback, useRef, useState } from "react";
import { CodeEditorProps } from "./interface";
import Editor, { type Monaco } from '@monaco-editor/react';
import './CodeEditor.css'

const options = {
  lineNumbers: 'on',
  guides: {
    indentation: false
  },
  automaticLayout: true,
  minimap: {
    enabled: false
  },
  scrollbar: {
    verticalScrollbarSize: 4,
    horizontalScrollbarSize: 8,
    alwaysConsumeMouseWheel: false
  },
  lineNumbersMinChars: 0,
  fontSize: 14,
  scrollBeyondLastLine: false,
  folding: true,
  overviewRulerBorder: false,
  tabSize: 2,
  readOnly: false,
  contextmenu: true
};

const CodeEditor:React.FC<CodeEditorProps> = ({
  defaultValue,
  value,
  onChange,
  resize,
  variables = [],
  defaultHeight = 200,
  onOpenModal,
  language = 'typescript',
  ...props
}) => {
  const [height, setHeight] = useState(defaultHeight);
  const initialY = useRef(0);

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    initialY.current = e.clientY;

    const handleMouseMove = (e: MouseEvent) => {
      const deltaY = e.clientY - initialY.current;
      initialY.current = e.clientY;
      setHeight((prevHeight) => (prevHeight + deltaY < 100 ? 100 : prevHeight + deltaY));
    };

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, []);

  const editorRef = useRef<any>(null);
  const monacoRef = useRef<Monaco | null>(null);

  const handleEditorDidMount = useCallback((editor: any, monaco: Monaco) => {
    editorRef.current = editor;
    monacoRef.current = monaco;
  }, []);

  const beforeMount = useCallback(
    (monaco: Monaco) => {
      monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
        validate: false,
        allowComments: false,
        schemas: [
          {
            uri: 'http://myserver/foo-schema.json',
            fileMatch: ['*'],
            schema: {}
          }
        ]
      });

      monaco.editor.defineTheme('JSONEditorTheme', {
        base: 'vs',
        inherit: true,
        rules: [{ token: 'variable', foreground: '2B5FD9' }],
        colors: {
          'editor.background': '#ffffff00',
          'editorLineNumber.foreground': '#aaa',
          'editorOverviewRuler.border': '#ffffff00',
          'editor.lineHighlightBackground': '#F7F8FA',
          'scrollbarSlider.background': '#E8EAEC',
          'editorIndentGuide.activeBackground': '#ddd',
          'editorIndentGuide.background': '#eee'
        }
      });
    },
    []
  );

  return (
    <div
      position={'relative'}
      className="border-1 rounded-md border-gray-200"
      style={{height: height + 'px'}}
      {...props}
    >
      <Editor
        height={'100%'}
        language={language}
        options={options as any}
        theme="JSONEditorTheme"
        beforeMount={beforeMount}
        defaultValue={defaultValue}
        value={value}
        className="workflow-code-editor border-none"
        onChange={(e) => {
          onChange?.(e || '');
        }}
        onMount={handleEditorDidMount}
      />
      {/* TODO resize */}
      {/* TODO full screen */}
    </div>
  );
};

export const CodeEditorMemo = React.memo(CodeEditor);