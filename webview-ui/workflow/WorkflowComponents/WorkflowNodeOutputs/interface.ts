import { WorkflowNodeOuput<PERSON>ield, ReactFlowNodeConfig } from "@lvjw/code-workflow-types";

export interface WorkflowNodeOutputsProps {
  outputs: WorkflowNodeOuputField[];
  node: ReactFlowNodeConfig;
}

export interface WorkflowNodeOutputFieldProps {
  output: WorkflowNodeOuputField;
  node: ReactFlowNodeConfig;
}


export interface WorkflowOutputFieldDialogProps {
  output: WorkflowNodeOuputField;
  node: ReactFlowNodeConfig;
  onSave?: (form: Partial<WorkflowNodeOuputField>) => Promise<void>;
}


export interface WorkflowNodeOutputFieldRenderProps {
  output: WorkflowNodeOuputField;
  node: ReactFlowNodeConfig;
}