import React from "react";
import { Select } from "@radix-ui/themes";
import { PrevNodeFieldSelector } from "../../common/PrevNodeFieldSelector/PrevNodeFieldSelector";
import { HTTPRequestConfigParam } from "@lvjw/code-workflow-types";

interface KeyValueTableProps {
  rows: HTTPRequestConfigParam[];
  setRows: (rows: HTTPRequestConfigParam[]) => void;
  keyPlaceholder?: string;
  valuePlaceholder?: string;
  currentNodeId?: string;
}

const KeyValueTable: React.FC<KeyValueTableProps> = ({
  rows,
  setRows,
  keyPlaceholder = "参数名",
  valuePlaceholder = "参数值",
  currentNodeId = "current-node",
}) => {
  // 兼容老数据结构
  const normalizeRows = (currentRows: HTTPRequestConfigParam[]) =>
    currentRows.map((row) => ({
      type: row.type || "const",
      key: row.key,
      value: row.value || "",
      valuePath: row.valuePath || [],
      valueLabelPath: row.valueLabelPath || [],
    }));

  const handleChange = (index: number, field: keyof HTTPRequestConfigParam, value: any) => {
    const newRows = normalizeRows(rows);
    (newRows[index] as any)[field] = value;
    setRows(newRows);
  };

  const handleTypeChange = (index: number, type: "const" | "dynamic") => {
    const newRows = normalizeRows(rows);
    newRows[index].type = type;
    setRows(newRows);
  };

  const handlePrevNodeFieldChange = (index: number, selected: { path: string[]; labelPath: string[] }) => {
    const newRows = normalizeRows(rows);
    newRows[index].valuePath = selected.path;
    newRows[index].valueLabelPath = selected.labelPath;
    setRows(newRows);
  };

  const handleAdd = () => {
    setRows([
      ...normalizeRows(rows),
      { type: "const", key: "", value: "", valuePath: [], valueLabelPath: [] },
    ]);
  };

  const handleRemove = (index: number) => {
    setRows(normalizeRows(rows).filter((_, i) => i !== index));
  };

  const normalizedRows = normalizeRows(rows);

  return (
    <div>
      {normalizedRows.map((row, idx) => (
        <div className="flex gap-2 mb-2 items-center" key={idx}>
          <input
            className="border rounded px-2 py-1"
            style={{ width: 80, height: 32 }}
            placeholder={keyPlaceholder}
            value={row.key}
            onChange={(e) => handleChange(idx, "key", e.target.value)}
          />
          <Select.Root value={row.type} onValueChange={(val: "const" | "dynamic") => handleTypeChange(idx, val)}>
            <Select.Trigger className="text-xs" style={{ width: 70, height: 32 }} />
            <Select.Content>
              <Select.Item value="const">静态</Select.Item>
              <Select.Item value="dynamic">动态</Select.Item>
            </Select.Content>
          </Select.Root>
          {row.type === "const" ? (
            <input
              className="flex-1 border rounded px-2 py-1"
              style={{ height: 32 }}
              placeholder={valuePlaceholder}
              value={row.value}
              onChange={(e) => handleChange(idx, "value", e.target.value)}
            />
          ) : (
            <div className="flex-1">
              <PrevNodeFieldSelector
                currentNodeId={currentNodeId!}
                valuePath={row.valuePath}
                valueLabelPath={row.valueLabelPath}
                onChange={(selected) => handlePrevNodeFieldChange(idx, selected)}
                triggerLabel="选择属性..."
              />
            </div>
          )}
          <button
            className="px-2 py-1 bg-red-100 text-red-600 rounded hover:bg-red-200"
            onClick={() => handleRemove(idx)}
          >
            删除
          </button>
        </div>
      ))}
      <button
        className="mt-2 px-4 py-1 bg-blue-100 text-blue-600 rounded hover:bg-blue-200"
        onClick={handleAdd}
      >
        添加
      </button>
    </div>
  );
};

export default KeyValueTable;