import React, { useEffect } from "react";
import { Theme, Button } from "@radix-ui/themes";
import { Background, ReactFlow, ReactFlowProvider } from "@xyflow/react";
import { ERNode } from "./ERNode";
import { Schema } from "./interface";
import { ChatPanel } from "./ChatPanel";
import { TableEditDrawer } from "./TableEditDrawer"; // Import the TableEditDrawer
import {
    useERDiagramStateControlled as useERDiagramState,
} from "./atoms";

function App() {
    // Use the custom hook to get state and actions
    const {
        nodes,
        edges,
        onNodesChange,
        onEdgesChange,
        triggerGenerateERDiagram,
    } = useERDiagramState();

    const nodeTypes = { erNode: ERNode };

    useEffect(() => {
        console.log("ER DiagramApp component mounted");

        const vscode = (window as any).vscode;
        if (vscode) {
            vscode.postMessage({ type: "getERDiagramContent" });
        } else {
            console.error("VSCode API not found");
        }

        const messageHandler = (event: MessageEvent) => {
            const message = event.data;
            console.log("App received message:", message);

            switch (message.type) {
                case "updateERDiagramContent":
                    const schema: Schema = JSON.parse(message.content);
                    triggerGenerateERDiagram(schema);
                    break;
            }
        };
        window.addEventListener("message", messageHandler);
        return () => {
            window.removeEventListener("message", messageHandler);
        };
    }, [triggerGenerateERDiagram]);

    const handleSave = () => {
        const vscode = (window as any).vscode;
        if (vscode) {
            vscode.postMessage({
                type: "saveERDiagramContent",
                content: {
                    nodes: nodes,
                    edges: edges,
                },
            });
            console.log("ER Diagram content sent to VS Code");
        } else {
            console.error("VSCode API not found, cannot save.");
        }
    };

    return (
        <ReactFlowProvider>
            <Theme className="w-full h-full">
                <div className="flex w-full h-full">
                    <div className="flex-1 h-full" style={{ position: "relative" }}>
                        <ReactFlow
                            nodes={nodes}
                            edges={edges}
                            onNodesChange={onNodesChange as any}
                            onEdgesChange={onEdgesChange as any}
                            fitView
                            nodeTypes={nodeTypes as any}
                        >
                            <Background />
                            <div
                                style={{
                                    position: "absolute",
                                    top: "10px",
                                    right: "10px",
                                    zIndex: 10,
                                }}
                            >
                                <Button onClick={handleSave}>保存</Button>
                            </div>
                        </ReactFlow>
                    </div>
                    <ChatPanel />
                </div>
                <TableEditDrawer /> {/* Add the TableEditDrawer here */}
            </Theme>
        </ReactFlowProvider>
    );
}

export default App;
