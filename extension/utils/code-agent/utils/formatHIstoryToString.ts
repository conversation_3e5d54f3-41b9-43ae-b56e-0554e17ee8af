import { ActionRecord } from "../types";
import { Logger } from "./logger";

export function formatHistoryToString(history: ActionRecord[], output?: (response: string) => void,
  logger?: Logger): string {
    if (!history || history.length === 0) {
        return "No previous actions.";
    }
    
    let historyStr = "\n";
    
    for (let i = 0; i < history.length; i++) {
        const action = history[i];
        
        // Header for all entries - removed timestamp
        historyStr += `Action ${i + 1}:\n`;
        historyStr += `- Tool: ${action.tool}\n`;
        
        // Add parameters
        const params = action.params || {};
        if (Object.keys(params).length > 0) {
            historyStr += `- Parameters:\n`;
            for (const [k, v] of Object.entries(params)) {
                historyStr += `  - ${k}: ${v}\n`;
            }
        }
        
        // Add detailed result information
        const result = action.result;
        if (result) {
            // Since result is string in our ActionRecord, we need to try parsing it as JSON
            let parsedResult: any;
            try {
                parsedResult = JSON.parse(result);
            } catch {
                // If parsing fails, treat as simple string result
                historyStr += `- Result: ${result}\n`;
                continue;
            }
            
            if (typeof parsedResult === 'object' && parsedResult !== null) {
                const success = parsedResult.success || action.success || false;
                historyStr += `- Result: ${success ? 'Success' : 'Failed'}\n`;
                
                // Add tool-specific details
                if (action.tool === 'readFile' && success) {
                    const content = parsedResult.content || "";
                    // Show full content without truncating
                    historyStr += `- Content: ${content}\n`;
                } else if (action.tool === 'grepSearch' && success) {
                    const matches = parsedResult.matches || [];
                    historyStr += `- Matches: ${matches.length}\n`;
                    // Show all matches without limiting to first 3
                    for (let j = 0; j < matches.length; j++) {
                        const match = matches[j];
                        historyStr += `  ${j + 1}. ${match.file || ''}:${match.line || match.line_number || ''}: ${match.content || ''}\n`;
                    }
                } else if (action.tool === 'editFile' && success) {
                    const operations = parsedResult.operations || 0;
                    historyStr += `- Operations: ${operations}\n`;
                    
                    // Include the reasoning if available
                    const reasoning = parsedResult.reasoning || "";
                    if (reasoning) {
                        historyStr += `- Reasoning: ${reasoning}\n`;
                    }
                } else if (action.tool === 'listDir' && success) {
                    // Get the tree visualization string
                    const treeVisualization = parsedResult.treeVisualization || "";
                    historyStr += "- Directory structure:\n";
                    
                    // Properly handle and format the tree visualization
                    if (treeVisualization && typeof treeVisualization === 'string') {
                        // First, ensure we handle any special line ending characters properly
                        const cleanTree = treeVisualization.replace(/\r\n/g, '\n').trim();
                        
                        if (cleanTree) {
                            // Add each line with proper indentation
                            for (const line of cleanTree.split('\n')) {
                                // Ensure the line is properly indented
                                if (line.trim()) {  // Only include non-empty lines
                                    historyStr += `  ${line}\n`;
                                }
                            }
                        } else {
                            historyStr += "  (No tree structure data)\n";
                        }
                    } else {
                        historyStr += "  (Empty or inaccessible directory)\n";
                        // Note: logger is not imported, so we skip the debug log
                    }
                } else {
                    // For other tools, just show the result as a string
                    historyStr += `- Result: ${JSON.stringify(parsedResult, null, 2)}\n`;
                }
            } else {
                historyStr += `- Result: ${result}\n`;
            }
        }
        
        // Add separator between actions
        if (i < history.length - 1) {
            historyStr += "\n";
        }
    }
    
    return historyStr;
}