import fs from 'fs/promises'
import path from 'path'
import { Logger } from './logger'

export async function insertFile(
  targetFile: string,
  content: string,
  lineNumber?: number,
  output?: (response: string) => void,
  logger?: Logger
): Promise<[string, boolean]> {
  try {
    // Create directories if they don't exist
    const dir = path.dirname(path.resolve(targetFile))
    await fs.mkdir(dir, { recursive: true })

    const fileExists = await checkFileExists(targetFile)

    // Complete file replacement or new file creation
    if (lineNumber === undefined) {
      let operation: string
      if (fileExists) {
        await fs.unlink(targetFile)
        operation = 'replaced'
      } else {
        operation = 'created'
      }

      // Create the file with new content
      await fs.writeFile(targetFile, content, 'utf-8')
      return [`Successfully ${operation} ${targetFile}`, true]
    }

    // Insert at specific line
    let lines: string[]
    let operation: string

    if (!fileExists) {
      // If file doesn't exist but line_number is specified, create it with empty lines
      lines = new Array(Math.max(0, lineNumber - 1)).fill('')
      operation = 'created and inserted into'
    } else {
      // Read existing content
      const fileContent = await fs.readFile(targetFile, 'utf-8')
      lines = fileContent.split('\n')
      operation = 'inserted into'
    }

    // Ensure line_number is valid
    if (lineNumber < 1) {
      return ['Error: Line number must be at least 1', false]
    }

    // Calculate insert position with 1-indexed to 0-indexed conversion
    const position = lineNumber - 1

    // If position is beyond the end, pad with newlines
    while (lines.length < position) {
      lines.push('')
    }

    // Insert content at specified position
    if (position === lines.length) {
      // Add at the end
      lines.push(content)
    } else {
      // Insert at the specified position
      lines.splice(position, 0, content)
    }

    // Write the updated content
    await fs.writeFile(targetFile, lines.join('\n'), 'utf-8')

    return [`Successfully ${operation} ${targetFile} at line ${lineNumber}`, true]
  } catch (error) {
    return [`Error inserting file: ${error}`, false]
  }
}

async function checkFileExists(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath)
    return true
  } catch {
    return false
  }
}