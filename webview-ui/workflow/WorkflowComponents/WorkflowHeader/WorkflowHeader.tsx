import { Button, DropdownMenu, Flex } from "@radix-ui/themes";
import { nodeInfoMap } from "../WorkflowNode";
import React, { useState } from "react";
import { WorkflowHeaderProps } from "./interface";
import { WorkflowRunDrawerContent } from "./WorkflowRunDrawerContent";
import Drawer from "../../LibComponents/Drawer";
import { WorkflowNodeTypeEnum } from "@lvjw/code-workflow-types";

const nodeOptions = nodeInfoMap.getNodeOptions()


export const WorkflowHeader = ({ workflowPath, onAddNode, onSave, onRun }: WorkflowHeaderProps) => {
  const [isRunDrawerOpen, setIsRunDrawerOpen] = useState(false);
  const toggleRunDrawer = () => setIsRunDrawerOpen(!isRunDrawerOpen);
  
  
  const handleRun = () => {
  
  }

  return (
    <Flex align="center" className="shadow-sm h-[40px] px-2">
      <div>TODO 工作流标题</div>
      <div className="flex flex-1 justify-center items-center">TODO 功能</div>
      <Flex gap="2">
        {/* 添加节点的下拉菜单 */}
        <DropdownMenu.Root>
          <DropdownMenu.Trigger>
            <Button variant="soft">
              添加节点
              <DropdownMenu.TriggerIcon />
            </Button>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content>
            {
              nodeOptions.map((option) => (
                <DropdownMenu.Item key={option.value} onSelect={() => onAddNode(option.value as WorkflowNodeTypeEnum)}>
                  {option.label}
                </DropdownMenu.Item>
              ))
            }
            {/* <DropdownMenu.Item key={WorkflowNodeTypeEnum.workflowStart} onSelect={() => onAddNode(WorkflowNodeTypeEnum.workflowStart)}>
              开始节点
            </DropdownMenu.Item>
            <DropdownMenu.Item key={WorkflowNodeTypeEnum.workflowEnd} onSelect={() => onAddNode(WorkflowNodeTypeEnum.workflowEnd)}>
              结束节点
            </DropdownMenu.Item>
            <DropdownMenu.Item key={WorkflowNodeTypeEnum.code} onSelect={() => onAddNode(WorkflowNodeTypeEnum.code)}>
              代码节点
            </DropdownMenu.Item> */}
          </DropdownMenu.Content>
        </DropdownMenu.Root>

        <Button onClick={onSave}>保存</Button>
       
        <Drawer
          open={isRunDrawerOpen}
          onOpenChange={setIsRunDrawerOpen}
          title="运行工作流"
          side="right"
          trigger={
            <Button onClick={handleRun}>运行</Button>
          }
          className="border-blue-200" // 自定义边框颜色示例
        >
          <WorkflowRunDrawerContent workflowPath={workflowPath} />
        </Drawer>
      </Flex>
    </Flex>
  );
};
