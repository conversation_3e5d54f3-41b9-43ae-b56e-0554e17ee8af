import { WorkspaceFolder, workspace, RelativePattern} from "vscode";
import { DatabaseTableInfo, DatabaseTableColunnInfo } from "./prompt"; // 确保 DatabaseTableColunnInfo 也被导出
import ts from "typescript";


function getDecoratorName(decorator: ts.Decorator): string | undefined {
    if (ts.isCallExpression(decorator.expression)) {
        const identifier = decorator.expression.expression;
        if (ts.isIdentifier(identifier)) {
            return identifier.text;
        }
    }
    return undefined;
}

function getDecoratorArguments(decorator: ts.Decorator): ts.NodeArray<ts.Expression> | undefined {
    if (ts.isCallExpression(decorator.expression)) {
        return decorator.expression.arguments;
    }
    return undefined;
}

function getObjectLiteralProperty(obj: ts.ObjectLiteralExpression, propertyName: string): string | undefined {
    const property = obj.properties.find(
        (prop) => ts.isPropertyAssignment(prop) && ts.isIdentifier(prop.name) && prop.name.text === propertyName
    );
    if (property && ts.isPropertyAssignment(property) && ts.isStringLiteral(property.initializer)) {
        return property.initializer.text;
    }
    return undefined;
}

export async function getTableAndColumnInfo(workspaceFolder: WorkspaceFolder): Promise<DatabaseTableInfo[]> {
    const modelFiles = await workspace.findFiles(new RelativePattern(workspaceFolder, '**/*.model.ts'), null);
    const databaseTableInfoList: DatabaseTableInfo[] = [];

    for (const fileUri of modelFiles) {
        try {
            const fileContent = await workspace.fs.readFile(fileUri);
            const code = Buffer.from(fileContent).toString('utf8');
            const sourceFile = ts.createSourceFile(
                fileUri.fsPath,
                code,
                ts.ScriptTarget.Latest,
                true
            );

            ts.forEachChild(sourceFile, (node) => {
                if (ts.isClassDeclaration(node) && node.name) {
                    const decorators = ts.getDecorators(node);
                    const entityDecorator = decorators?.find(d => getDecoratorName(d) === 'Entity');

                    if (entityDecorator) {
                        const className = node.name.text;
                        let tableName = className; // Default table name to class name
                        let tableComment = '';

                        const args = getDecoratorArguments(entityDecorator);
                        if (args && args.length > 0 && ts.isObjectLiteralExpression(args[0])) {
                            const entityOptions = args[0] as ts.ObjectLiteralExpression;
                            tableName = getObjectLiteralProperty(entityOptions, 'name') || tableName;
                            tableComment = getObjectLiteralProperty(entityOptions, 'comment') || '';
                        }

                        const columns: DatabaseTableColunnInfo[] = [];
                        node.members.forEach((member) => {
                            if (ts.isPropertyDeclaration(member) && member.name && ts.isIdentifier(member.name)) {
                                const propDecorators = ts.getDecorators(member);
                                const columnDecorator = propDecorators?.find(d => getDecoratorName(d) === 'Column' || getDecoratorName(d) === 'ManyToOne' || getDecoratorName(d) === 'OneToMany');
                                const primaryKeyDecorator = propDecorators?.find(d => getDecoratorName(d) === 'PrimaryGeneratedColumn');

                                if (columnDecorator || primaryKeyDecorator) {
                                    const columnName = member.name.text;
                                    let columnComment = '';
                                    let isPrimaryKey = !!primaryKeyDecorator; // True if PrimaryGeneratedColumn decorator exists
                                    let columnType = 'unknown'; // Default type

                                    if (member.type && ts.isTypeNode(member.type)) {
                                        // Get the string representation of the type
                                        // This might need a more robust way to get the exact type string depending on complexity
                                        columnType = member.type.getText(sourceFile); 
                                    }

                                    if (columnDecorator) {
                                        const colArgs = getDecoratorArguments(columnDecorator);
                                        if (colArgs && colArgs.length > 0 && ts.isObjectLiteralExpression(colArgs[0])) {
                                            const columnOptions = colArgs[0] as ts.ObjectLiteralExpression;
                                            columnComment = getObjectLiteralProperty(columnOptions, 'comment') || '';
                                            // Potentially extract 'type' from @Column options if specified explicitly, 
                                            // e.g., @Column({ type: 'varchar', length: 255 })
                                            const typeFromDecorator = getObjectLiteralProperty(columnOptions, 'type');
                                            if (typeFromDecorator) {
                                                columnType = typeFromDecorator; // Prefer type from decorator if available
                                            }
                                        }
                                    }
                                    
                                    columns.push({
                                        name: columnName,
                                        comment: columnComment,
                                        isPrimaryKey: isPrimaryKey,
                                        type: columnType,
                                    });
                                }
                            }
                        });

                        databaseTableInfoList.push({
                            name: tableName,
                            comment: tableComment,
                            columns,
                        });
                    }
                }
            });
        } catch (error) {
            console.error(`Error processing file ${fileUri.fsPath}:`, error);
        }
    }

    return databaseTableInfoList;
}