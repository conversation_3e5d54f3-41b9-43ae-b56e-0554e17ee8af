import React, { useState, useEffect, useRef } from 'react';
import { Theme } from '@radix-ui/themes';
import { ChatHeader } from './components/ChatHeader';
import { MessageList } from './components/MessageList';
import { QuickActions } from './components/QuickActions';
import { ChatInput } from './components/ChatInput';

declare global {
  interface Window {
    webviewInfo?: {
      viewType: string;
      title: string;
      panelId: string;
    };
    vscode: any;
  }
}

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

function App() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      role: 'assistant',
      content: '你好！我是AI编程助手，可以帮助你解决编程问题、代码审查、优化建议等。有什么我可以帮助你的吗？',
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: inputValue.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // 发送消息到扩展
      if (window.vscode) {
        window.vscode.postMessage({
          type: 'sendMessage',
          content: userMessage.content
        });
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      // 添加错误消息
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: '抱歉，发送消息时出现错误，请稍后重试。',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
      setIsLoading(false);
    }
  };

  const handleClearChat = () => {
    setMessages([{
      id: '1',
      role: 'assistant',
      content: '你好！我是AI编程助手，可以帮助你解决编程问题、代码审查、优化建议等。有什么我可以帮助你的吗？',
      timestamp: new Date()
    }]);
  };

  const handleNewChat = () => {
    // 清空当前聊天记录
    setMessages([{
      id: '1',
      role: 'assistant',
      content: '你好！我是AI编程助手，可以帮助你解决编程问题、代码审查、优化建议等。有什么我可以帮助你的吗？',
      timestamp: new Date()
    }]);
    
    // 清空输入框
    setInputValue('');
    
    // 重置加载状态
    setIsLoading(false);
    
    // 通知后端清理代码代理
    if (window.vscode) {
      window.vscode.postMessage({
        type: 'newChat'
      });
    }
  };

  const insertQuickText = (text: string) => {
    setInputValue(prev => prev + text);
    textareaRef.current?.focus();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 监听来自扩展的消息
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      if (message.type === 'aiResponse') {
        const assistantMessage: Message = {
          id: Date.now().toString(),
          role: 'assistant',
          content: message.content,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, assistantMessage]);
        setIsLoading(false);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  return (
    <Theme>
      <div className="h-screen flex flex-col vscode-bg-primary vscode-text-primary px-4">
        {/* 顶部标题栏 */}
        <ChatHeader onClearChat={handleClearChat} onNewChat={handleNewChat} />

        {/* 消息区域 */}
        <MessageList 
          messages={messages}
          isLoading={isLoading}
          messagesEndRef={messagesEndRef}
        />

        {/* 底部输入区域 */}
        <div className="border-t vscode-border vscode-bg-primary">
          <ChatInput
            value={inputValue}
            onChange={setInputValue}
            onSend={handleSendMessage}
            onKeyPress={handleKeyPress}
            isLoading={isLoading}
            textareaRef={textareaRef}
            onInsertText={insertQuickText}
          />
        </div>
      </div>
    </Theme>
  );
}

export default App;