import { Flow } from "pocketflow";
import { RequirementAnalysisNode, AskClarificationNode, GenerateTableNode, Actions } from "./nodes";

const MAX_RETRIES = 3;

const requirementAnalysisNode = new RequirementAnalysisNode(MAX_RETRIES);
const askClarificationNode = new AskClarificationNode(MAX_RETRIES);
const generateTableNode = new GenerateTableNode(MAX_RETRIES);

requirementAnalysisNode.on(Actions.NeedClarification, askClarificationNode);
requirementAnalysisNode.on(Actions.WriteTableDoc, generateTableNode);

askClarificationNode.on(Actions.AnalyzeRequirement, requirementAnalysisNode);

export const flow = new Flow(requirementAnalysisNode);