import * as vscode from "vscode";
import { ConnectionManager } from "../utils/connection/connection-manager";
import * as path from "path";
import { getNonce } from "../utils/nonce";
import { SharedStorage } from "../utils/table-operation/nodes";
import { flow } from "../utils/table-operation/flow";
import { promises as fsPromises } from "fs"; // Added import for fs.promises
import { getHtmlForWebview } from "./utils";

export class ERDiagramWebviewContainer {
    private panels: Map<string, vscode.WebviewPanel> = new Map();
    private readonly maxPanels: number = 3;

    constructor(
        private readonly context: vscode.ExtensionContext,
        private connectionManager: ConnectionManager
    ) {}

    private getSchemaKey(
        connectionId: string,
        databaseName: string,
        schemaName: string
    ): string {
        return `${connectionId}|${databaseName}|${schemaName}`;
    }

    private async _generateModelFile(
        node: TableNode,
        targetBaseDir: string
    ): Promise<void> {
        if (!node.data || node.data.type !== "table" || !node.data.name) {
            // vscode.window.showWarningMessage(`Skipping node ID ${node.id}: Invalid data or not a table type.`);
            console.warn(
                `Skipping node ID ${node.id}: Invalid data or not a table type. Node data:`,
                node.data
            );
            return;
        }

        const entityName = node.data.name;
        const className = toPascalCase(entityName);
        const fileName = `${className}.model.ts`;
        const filePath = path.join(targetBaseDir, fileName);

        try {
            // Check if file already exists
            await fsPromises.access(filePath, fsPromises.constants.F_OK);
            // If access does not throw, file exists
            vscode.window.showInformationMessage(
                `Skipping file creation: ${filePath} already exists.`
            );
            console.log(`Skipping file creation: ${filePath} already exists.`);
            return; // Skip writing the file
        } catch (error) {
            // If error is anything other than ENOENT, it's an unexpected error
            // If error.code is ENOENT, file does not exist, proceed to write
            // @ts-ignore
            if (error.code !== "ENOENT") {
                vscode.window.showErrorMessage(
                    `Error checking file ${filePath}: ${
                        error instanceof Error ? error.message : String(error)
                    }`
                );
                console.error(`Error checking file ${filePath}:`, error);
                return; // Don't proceed if there was an unexpected error checking file
            }
            // File does not exist, so we can proceed to create it (ENOENT error is expected here)
        }

        let columnsContent = "";
        let primaryKeyColumnExists = false;

        for (const column of node.data.columns) {
            let columnOptions = "";
            const optionsArray: string[] = [];

            if (column.isPrimaryKey) {
                primaryKeyColumnExists = true;
                columnsContent += `  @PrimaryGeneratedColumn()
  ${column.name}: number; // Assuming primary keys are numbers, adjust if not

`;
                continue; // Skip other decorators if it's a PrimaryGeneratedColumn
            }

            if (column.isUnique) {
                optionsArray.push("unique: true");
            }
            if (column.isNullable === false) {
                // Explicitly check for false, as undefined means nullable by default for some types
                optionsArray.push("nullable: false");
            } else if (column.isNullable === true) {
                optionsArray.push("nullable: true");
            }

            if (column.defaultValue !== undefined) {
                optionsArray.push(
                    `default: ${JSON.stringify(column.defaultValue)}`
                );
            }
            if (column.comment) {
                optionsArray.push(
                    `comment: "${column.comment.replace(/"/g, '"')}"`
                );
            }

            // Basic TypeORM to TypeScript type mapping
            let tsType = "string"; // Default type
            const colTypeLower = column.type.toLowerCase();
            if (
                colTypeLower.includes("int") ||
                colTypeLower.includes("serial") ||
                colTypeLower.includes("long") ||
                colTypeLower.includes("short")
            ) {
                tsType = "number";
            } else if (colTypeLower.includes("bool")) {
                tsType = "boolean";
            } else if (
                colTypeLower.includes("date") ||
                colTypeLower.includes("time")
            ) {
                tsType = "Date";
            } else if (
                colTypeLower.includes("json") ||
                colTypeLower.includes("array")
            ) {
                tsType = "any"; // Or a more specific type if known
            } else if (
                colTypeLower.includes("decimal") ||
                colTypeLower.includes("numeric") ||
                colTypeLower.includes("float") ||
                colTypeLower.includes("double")
            ) {
                tsType = "number";
                if (column.precision !== undefined)
                    optionsArray.push(`precision: ${column.precision}`);
                if (column.scale !== undefined)
                    optionsArray.push(`scale: ${column.scale}`);
            } else if (
                colTypeLower.includes("char") ||
                colTypeLower.includes("text")
            ) {
                tsType = "string";
                if (column.length !== undefined)
                    optionsArray.push(`length: ${column.length}`);
            }

            if (optionsArray.length > 0) {
                columnOptions = `{ ${optionsArray.join(", ")} }`;
            }

            columnsContent += `  @Column(${columnOptions})
  ${column.name}: ${tsType};

`;
        }

        // If no primary key was defined via isPrimaryKey, add a default one.
        if (!primaryKeyColumnExists) {
            columnsContent =
                `  @PrimaryGeneratedColumn()
  id: number;

` + columnsContent;
        }

        const tableComment = node.data.comment
            ? `comment: "${node.data.comment.replace(/"/g, '"')}"`
            : `comment: "${className} table"`;

        const fileContent = `import { Entity, PrimaryGeneratedColumn, Column, BaseEntity ${
            primaryKeyColumnExists
                ? ""
                : "" /* Add other imports like Index if needed */
        } } from "typeorm";
// import { Min, Max, IsEmail } from "class-validator"; // Add if validation is needed

@Entity({
    name: "${entityName.toLowerCase()}",
    ${tableComment}
})
export class ${className} extends BaseEntity {
${columnsContent}}
`;

        try {
            await fsPromises.writeFile(filePath, fileContent, "utf8");
            vscode.window.showInformationMessage(
                `Created model file: ${filePath}`
            );
        } catch (error) {
            vscode.window.showErrorMessage(
                `Failed to write model file ${filePath}: ${
                    error instanceof Error ? error.message : String(error)
                }`
            );
            console.error(`Failed to write model file ${filePath}:`, error);
        }
    }

    public async showSchemaERDiagram(
        connectionId: string,
        databaseName: string,
        schemaName: string
    ): Promise<void> {
        try {
            console.debug(
                `Showing ER diagram for schema ${databaseName}.${schemaName}`
            );

            const schemaKey = this.getSchemaKey(
                connectionId,
                databaseName,
                schemaName
            );
            const existingPanel = this.panels.get(schemaKey);

            if (existingPanel) {
                existingPanel.reveal(vscode.ViewColumn.Active);
                return;
            }

            // 超过最大数量，关闭最早的
            if (this.panels.size >= this.maxPanels) {
                const firstKey = this.panels.keys().next().value;
                this.panels.get(firstKey!)?.dispose();
                this.panels.delete(firstKey!);
            }

            const panel = this.createPanel(schemaName);
            this.panels.set(schemaKey, panel);

            // 关闭时移除
            panel.onDidDispose(() => {
                this.panels.delete(schemaKey);
                console.debug(`Panel for schema ${schemaName} was closed`);
            });

            // 获取数据库结构
            const dbStructure = await this.connectionManager.getDbStructure(
                connectionId,
                databaseName
            );
            const schemaStructure = dbStructure.schemas.find(
                (s) => s.name === schemaName
            );

            if (!schemaStructure) {
                throw new Error(`Schema ${schemaName} not found`);
            }

            // 等待 webview 加载完成后发送数据
            panel.webview.onDidReceiveMessage(async (message) => {
                if (message.type === "getERDiagramContent") {
                    panel.webview.postMessage({
                        type: "updateERDiagramContent",
                        content: JSON.stringify(schemaStructure),
                    });
                }
                // 处理聊天消息
                else if (message.type === "createTableChatUserMessage") {
                    const userContent = message.content;
                    const sessionId = message.sessionId;

                    const shared: SharedStorage = {
                        userRequirement: userContent,
                        clarificationQuestions: [],
                        clarification: "",
                        dbTableDoc: "",
                        panel: panel,
                        sessionId: sessionId,
                        workspaceFolder: vscode.workspace.workspaceFolders![0],
                    };

                    shared.workspaceFolder =
                        vscode.workspace.workspaceFolders![0];
                    flow.run(shared);
                }
                // Handle saveERDiagramContent message
                else if (message.type === "saveERDiagramContent") {
                    console.log("Received ER Diagram content from webview:");
                    if (message.content?.nodes) {
                        const graphData: GraphData = message.content;
                        console.log("Received ER Diagram content:", graphData);

                        const workspaceFolder =
                            vscode.workspace.workspaceFolders?.[0];
                        if (!workspaceFolder) {
                            vscode.window.showErrorMessage(
                                "No workspace folder found. Cannot save models."
                            );
                            return;
                        }

                        const targetBaseDir = path.join(
                            workspaceFolder.uri.fsPath,
                            "src",
                            "low-code-models"
                        );

                        try {
                            await fsPromises.mkdir(targetBaseDir, {
                                recursive: true,
                            });

                            for (const node of graphData.nodes) {
                                await this._generateModelFile(
                                    node,
                                    targetBaseDir
                                );
                            }
                        } catch (error) {
                            vscode.window.showErrorMessage(
                                `Failed to create model files: ${
                                    error instanceof Error
                                        ? error.message
                                        : String(error)
                                }`
                            );
                            console.error(
                                "Failed to create model files:",
                                error
                            );
                        }
                    }
                }
                // Handle new message types from the flow nodes
                else if (message.type === "requirementAnalysisResult") {
                    // Process requirementAnalysisResult
                    // For example, display questions if clarification is needed
                    console.log(
                        "Received requirement analysis result:",
                        message.payload
                    );

                    // You might want to send this to the webview UI or update UI based on payload
                } else if (message.type === "clarificationProcessComplete") {
                    // Process clarificationProcessComplete
                    console.log(
                        "Received clarification process complete confirmation:",
                        message.payload
                    );
                    // You might want to update UI to show that clarification is done
                }
                // Note: 'clarificationAnswersFromWebview' is handled directly by the Promise in AskClarificationNode.exec
                // So, you typically don't need a separate 'else if' block here for it.
            });
        } catch (error) {
            vscode.window.showErrorMessage(
                `Failed to show ER diagram: ${
                    error instanceof Error ? error.message : String(error)
                }`
            );
        }
    }

    private createPanel(title: string): vscode.WebviewPanel {
        const panel = vscode.window.createWebviewPanel(
            "erDiagram",
            `ER Diagram - ${title}`,
            vscode.ViewColumn.Active,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(
                        this.context.extensionUri,
                        "dist"
                    )
                ],
            }
        );

        panel.webview.html = getHtmlForWebview(
            vscode.Uri.joinPath(
                this.context.extensionUri,
                "dist",
                "er-diagram",
                "index.html"
            ),
            panel.webview,
            this.context.extensionUri
        );
        return panel;
    }
}
interface GraphData {
    nodes: TableNode[];
    // TODO: 定义 edges 类型
    edges: any[];
}

interface TableNode {
    data: TableNodeData;
}

interface TableNodeData {
    name: string;
    type: "table";
    columns: TableColumnData[];
}

// Helper function to convert string to PascalCase
function toPascalCase(str: string): string {
    return str
        .split(/[^a-zA-Z0-9]/) // Split by non-alphanumeric characters
        .map(
            (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        )
        .join("");
}

// Interface definitions based on the provided file context
interface TableColumnData {
    name: string;
    type: string; // e.g., 'INT', 'VARCHAR', 'BOOLEAN', 'TEXT', 'DATETIME'
    isPrimaryKey?: boolean; // Optional, as it might not always be present
    comment?: string; // Optional
    // Add other potential properties like isNullable, defaultValue, etc. if needed
    isNullable?: boolean;
    defaultValue?: any;
    length?: number;
    precision?: number;
    scale?: number;
    isUnique?: boolean;
}

interface TableNodeData {
    name: string;
    type: "table"; // Assuming type is always 'table' for these nodes
    columns: TableColumnData[];
    comment?: string; // Optional table comment
}

interface TableNode {
    id: string; // Or whatever type the id is
    data: TableNodeData;
    // other properties like position, type (React Flow node type), etc.
}

interface GraphData {
    nodes: TableNode[];
    edges: any[]; // Define edges type more specifically if possible
}
