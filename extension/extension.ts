import * as vscode from 'vscode'
import { WorkflowTreeDataProvider } from './TreeViews/WorkflowTreeDataProvider'
import { ConnectionManager } from './utils/connection/connection-manager'
import { DatabaseTreeDataProvider } from './TreeViews/DatabaseTreeDataProvider'
import { ConnectionWebviewContainer } from './webview/ConnectionWebview'
import { TableDataWebviewContainer } from './webview/TableDataWebview'
import { TableDataQueryWebviewContainer } from './webview/TableDataQueryWebview'
import { ApiDocsWebviewContainer } from './webview/ApiDocsWebview'
import { ERDiagramWebviewContainer } from './webview/ERDiagramWebview'
import { UserProfileWebviewContainer } from './webview/UserProfileWebview'
import { LoginWebviewContainer } from './webview/LoginWebview'
import { WorkflowWebviewPanelContainer } from './webview/WorkflowWebviewPanel'
import { AIChatWebviewProvider } from './webview/AIChatWebviewProvider'


export function activate(context: vscode.ExtensionContext) {
    console.log('Congratulations, your extension dev-expert is now active!')

    // 注册 URI 处理器
    context.subscriptions.push(
        vscode.window.registerUriHandler({
            handleUri(uri: vscode.Uri) {
                console.log('Received URI:', JSON.stringify(uri))
                if (uri.path === '/auth/login') {
                    const userInfo = uri.query.split('=')[1]
                    if (userInfo) {
                        // 更新登录状态
                        context.globalState.update('isLoggedIn', true)
                        context.globalState.update('username', userInfo)
                        vscode.window.showInformationMessage('登录成功')
                        // 重新加载窗口
                        vscode.commands.executeCommand('workbench.action.reloadWindow')
                    }
                }
            },
        }),
    )

    // 检查登录状态
    const isLoggedIn = context.globalState.get('isLoggedIn')
    if (!isLoggedIn) {
        const loginWebviewContainer = new LoginWebviewContainer(context)
        loginWebviewContainer.openLoginWebviewPanel()
        return
    }

    const treeDataProvider = new WorkflowTreeDataProvider()
    vscode.window.createTreeView('flowTreeView', { treeDataProvider })
    // 监听变化
    const fileWatcher = vscode.workspace.createFileSystemWatcher('**/*.flow.json')
    fileWatcher.onDidCreate(() => treeDataProvider.refresh())
    fileWatcher.onDidDelete(() => treeDataProvider.refresh())
    fileWatcher.onDidChange(() => treeDataProvider.refresh())

    // 数据库连接开始
    const connectionManager = new ConnectionManager(context)
    const connectionsProvider = new DatabaseTreeDataProvider(connectionManager)
    const connectionWebviewContainer = new ConnectionWebviewContainer(
        context,
        connectionManager,
        connectionsProvider,
    )
    const tableDataWebviewContainer = new TableDataWebviewContainer(context, connectionManager)
    const tableDataQueryWebviewContainer = new TableDataQueryWebviewContainer(
        context,
        connectionManager,
    )
    const erDiagramWebviewContainer = new ERDiagramWebviewContainer(context, connectionManager)
    context.subscriptions.push(
        vscode.window.registerTreeDataProvider('databaseConnections', connectionsProvider),
        // 添加连接
        vscode.commands.registerCommand('dev-expert.addConnection', () => {
            console.debug('Command executed: addConnection')
            connectionWebviewContainer.openConnectionWebviewPanel()
            // return databaseExplorer.addConnection();
        }),
        // 移除连接
        vscode.commands.registerCommand('dev-expert.removeConnection', async (node: any) => {
            console.debug(
                `Command executed: removeConnection with node: ${
                    node ? JSON.stringify(node) : 'undefined'
                }`,
            )

            if (!node || !node.connectionId) {
                vscode.window.showErrorMessage('无法删除连接：无效的连接节点')
                return
            }

            try {
                // 显示确认对话框
                const confirm = await vscode.window.showWarningMessage(
                    `确定要删除连接 "${node.label}" 吗？`,
                    { modal: true },
                    '确定',
                )

                if (confirm === '确定') {
                    // 调用 ConnectionManager 的删除方法
                    await connectionManager.removeConnection(node.connectionId)
                    // 刷新树视图
                    connectionsProvider.refresh()
                    vscode.window.showInformationMessage(`已删除连接 "${node.label}"`)
                }
            } catch (error) {
                vscode.window.showErrorMessage(
                    `删除连接失败: ${error instanceof Error ? error.message : String(error)}`,
                )
            }
        }),
        // 刷新连接
        vscode.commands.registerCommand('dev-expert.refreshConnection', (node: any) => {
            console.debug(
                `Command executed: refreshConnection with node: ${
                    node ? JSON.stringify(node) : 'undefined'
                }`,
            )
            // return databaseExplorer.refreshConnection(node);
        }),
        // 显示表数据
        vscode.commands.registerCommand('dev-expert.showTableData', (node: any) => {
            console.debug(
                `Command executed: showTableData with node: ${
                    node ? JSON.stringify(node) : 'undefined'
                }`,
            )
            if (node && node.connectionId && node.databaseName && node.tableName) {
                // show dialog
                vscode.window.showInformationMessage(
                    `显示表数据: ${node.connectionId}, ${node.databaseName}.${node.tableName}`,
                )
            }
            tableDataWebviewContainer.showTableData(
                node.connectionId,
                node.databaseName,
                node.tableName,
                node.schemaName,
            )
        }),
        // 打开查询控制台
        vscode.commands.registerCommand('dev-expert.openQueryConsole', () => {
            console.debug('Command executed: openQueryConsole')
            tableDataQueryWebviewContainer.openQueryConsole()
        }),
        // 显示 ER 图
        vscode.commands.registerCommand('dev-expert.showSchemaERDiagram', (node: any) => {
            console.debug(
                `Command executed: showSchemaERDiagram with node: ${
                    node ? JSON.stringify(node) : 'undefined'
                }`,
            )
            if (node && node.connectionId && node.databaseName && node.schemaName) {
                erDiagramWebviewContainer.showSchemaERDiagram(
                    node.connectionId,
                    node.databaseName,
                    node.schemaName,
                )
            }
        }),
        // 退出登录
        vscode.commands.registerCommand('dev-expert.logout', async () => {
            try {
                // 显示确认对话框
                const confirm = await vscode.window.showWarningMessage(
                    '确定要退出登录吗？',
                    { modal: true },
                    '确定',
                )

                if (confirm === '确定') {
                    // 清除登录状态
                    await context.globalState.update('isLoggedIn', false)
                    await context.globalState.update('username', undefined)

                    // 显示退出成功提示
                    vscode.window.showInformationMessage('已成功退出登录')

                    // 重新加载窗口
                    vscode.commands.executeCommand('workbench.action.reloadWindow')
                }
            } catch (error) {
                vscode.window.showErrorMessage(
                    `退出登录失败: ${error instanceof Error ? error.message : String(error)}`,
                )
            }
        }),
    )
    // 数据库连接结束

    // 工作流开始
    const workflowWebviewPanelContainer = new WorkflowWebviewPanelContainer(
        context,
        connectionManager,
    )
    context.subscriptions.push(
        vscode.commands.registerCommand(
            'dev-expert.openWorkflowWebview',
            (basename: string, uri: vscode.Uri) => {
                console.log('new new new')
                workflowWebviewPanelContainer.show(basename, uri)
            },
        ),
    )
    // 工作流结束

    // API 文档查看开始
    const apiDocsWebviewContainer = new ApiDocsWebviewContainer(context)
    context.subscriptions.push(
        vscode.commands.registerCommand('dev-expert.showApiDocs', () => {
            console.debug('Command executed: showApiDocs')
            apiDocsWebviewContainer.openApiDocsWebviewPanel()
        }),
    )
    // API 文档查看结束

    // 用户详情页
    const userProfileWebviewContainer = new UserProfileWebviewContainer(context)
    context.subscriptions.push(
        vscode.commands.registerCommand('dev-expert.showUserProfile', () => {
            console.debug('Command executed: showUserProfile')
            userProfileWebviewContainer.show()
        }),
    )
    // 用户详情页结束

    // AI编程助手开始
    const aiChatWebviewProvider = new AIChatWebviewProvider(context)
    
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider(
            AIChatWebviewProvider.viewType,
            aiChatWebviewProvider
        ),
    )
    // AI编程助手结束
}

// This method is called when your extension is deactivated
export function deactivate() {}
