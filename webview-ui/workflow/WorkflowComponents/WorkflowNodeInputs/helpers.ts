import { ReactFlowNodeConfig, WorkflowEdge } from "@lvjw/code-workflow-types";
import { CascaderItem } from "../../LibComponents/Cascader";
import { HTTPRequestConfigParam } from "@lvjw/code-workflow-types";

// 构建图的邻接表
export const buildGraph = (nodes: ReactFlowNodeConfig[], edges: WorkflowEdge[]) => {
  const adjacencyList = new Map<string, Set<string>>();
  
  // 初始化邻接表
  nodes.forEach(node => {
    adjacencyList.set(node.id, new Set());
  });

  // 添加边
  edges.forEach(edge => {
    const sourceNode = adjacencyList.get(edge.source);
    if (sourceNode) {
      sourceNode.add(edge.target);
    }
  });

  return adjacencyList;
};

// 使用 BFS 获取所有前置节点
export const getPreviousNodes = (graph: Map<string, Set<string>>, currentNodeId: string) => {
  const visited = new Set<string>();
  const queue: string[] = [currentNodeId];
  const previousNodes = new Set<string>();

  while (queue.length > 0) {
    const currentId = queue.shift()!;
    
    // 遍历所有节点，找到指向当前节点的边
    for (const [nodeId, targets] of graph.entries()) {
      if (targets.has(currentId) && !visited.has(nodeId)) {
        visited.add(nodeId);
        previousNodes.add(nodeId);
        queue.push(nodeId);
      }
    }
  }

  return Array.from(previousNodes);
};

// 构建 Cascader 数据
export const buildCascaderData = (nodes: ReactFlowNodeConfig[], previousNodeIds: string[]): CascaderItem[] => {
  const previousNodesData = nodes.filter(node => previousNodeIds.includes(node.id));
  
  return previousNodesData.map(node => ({
    value: node.id,
    label: node.data.label,
    children: [
      {
        value: 'inputs',
        label: '输入',
        children: node.data.inputs?.map(input => ({
          value: input.id,
          label: `${input.label || input.attrName}`
        })) || []
      },
      {
        value: 'outputs',
        label: '输出',
        children: node.data.outputs?.map(output => ({
          value: output.id,
          label: `${output.label || output.attrName}`
        })) || []
      }
    ]
  }));
};

export const extractParamsFromUrl = (url: string): { pathParams: HTTPRequestConfigParam[], queryParams: HTTPRequestConfigParam[] } => {
  const pathParams: HTTPRequestConfigParam[] = [];
  const queryParams: HTTPRequestConfigParam[] = [];

  if (!url) {
    return { pathParams, queryParams };
  }

  try {
    // 提取路径参数 (e.g., /:id or /{id})
    // 使用更精确的正则表达式，确保只匹配有效的参数名
    const pathParamMatches = Array.from(url.matchAll(/[:]([a-zA-Z0-9_]+(?=\/|$|\?))|\/{([a-zA-Z0-9_]+)}/g));
    pathParamMatches.forEach((match) => {
      const variable = match[1] || match[2]; // match[1] for :id, match[2] for {id}
      if (variable && !pathParams.some((p) => p.key === variable)) {
        pathParams.push({ type: "const", key: variable, value: "", valuePath: [], valueLabelPath: [] });
      }
    });

    // 提取查询参数 (e.g., ?id={id} or ?name=value)
    const queryStringMatch = url.match(/\?([^#]*)/);
    if (queryStringMatch && queryStringMatch[1]) {
      const searchParams = new URLSearchParams(queryStringMatch[1]);
      searchParams.forEach((value, key) => {
        // 如果查询参数的值是 {some_variable} 形式，我们提取 some_variable 作为 key
        // 否则直接用 key
        const valueAsPlaceholderMatch = value.match(/^{([^}]+)}$/);
        let paramKey = key;
        let paramValue = value;
        if (valueAsPlaceholderMatch) {
          paramKey = valueAsPlaceholderMatch[1];
          paramValue = ""; // 如果是占位符，值设为空
        }
        if (!queryParams.some((p) => p.key === paramKey)) {
          queryParams.push({ type: "const", key: paramKey, value: paramValue, valuePath: [], valueLabelPath: [] });
        }
      });
    }
  } catch (error) {
    console.warn("Error extracting params from URL:", url, error);
    // 在发生错误时返回空的参数列表，而不是抛出错误
    return { pathParams: [], queryParams: [] };
  }

  return { pathParams, queryParams };
};