import React, { useMemo } from "react"
import { NodeBasicProps } from "./interface"
import NodeContainer from "./components/NodeContainer";
import { IOContainer } from "./components/IOContainer";
import IOTitle from "./components/IOTitle";
import { useNodeInputsState, useNodeOutputsState } from "../WorkflowCanvas";
import WorkflowNodeInputs from "../WorkflowNodeInputs";
import WorkflowNodeOutputs from "../WorkflowNodeOutputs";

export function SimpleWorkflowNode (props:NodeBasicProps) {
    const { data, id } = props;
    const { inputs } = useNodeInputsState(id)
    const { outputs } = useNodeOutputsState(id)

    const Inputs = useMemo(() => {
        return <WorkflowNodeInputs inputs={inputs} node={props as any} />
    }, [inputs])

    const Outputs = useMemo(() => {
        return <WorkflowNodeOutputs outputs={outputs} node={props as any} />
    }, [outputs])


    const renderer = useMemo(() => {
        return <NodeContainer label={data.label} intro={data.intro} icon={data.icon}>
            {data.showInputs && <IOContainer>
                <IOTitle type="input" text="输入" nodeId={id} addable={true}/>
                {Inputs}
            </IOContainer>}
            {data.showOutputs && <IOContainer>
                <IOTitle type="output" text="输出" nodeId={id} addable={false}/>
                {Outputs}
            </IOContainer>}
        </NodeContainer>
    }, [id, data.label, data.intro, data.icon, Inputs, Outputs])
    return renderer
}

export const  MemorizedSimpleWorkflowNode =  React.memo(SimpleWorkflowNode)
