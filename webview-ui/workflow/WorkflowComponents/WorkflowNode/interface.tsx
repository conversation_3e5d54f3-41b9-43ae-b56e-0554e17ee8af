import { NodeProps } from "@xyflow/react";
import { FlexProps } from "@radix-ui/themes";
import { ReactFlowNodeConfig } from "@lvjw/code-workflow-types";

export interface NodeContainerProps {
  children: React.ReactNode | React.ReactNode[] | string;
  hasCustomHandle?: boolean;
  icon?: string;
  label: string;
  intro?: string;
  nodeId?: string;
  minWidth?: string | number;
  maxWidth?: string | number;
  minHeight?: string | number;
  maxHeight?: string | number;
  width?: string | number;
  height?: string | number;
  selected?: boolean;
  isError?: boolean;
  isFolded?: boolean;
  isSelected?: boolean;
  editable?: boolean;
  customStyle?: FlexProps;
}

export type NodeBasicProps = NodeProps<ReactFlowNodeConfig>;

export interface IOTitleProps {
  text: string;
  nodeId: string;
  type: "input" | "output";
  addable: boolean;
}
