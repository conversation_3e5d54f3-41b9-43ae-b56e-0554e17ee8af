import React, { useState, useEffect } from "react";
import { StringWithLLMRendererProps } from "./interface";
import { Button, TextArea } from "@radix-ui/themes";

export const StringWithLLMRenderer: React.FC<StringWithLLMRendererProps> = ({
  input,
  node,
  value,
  onChange,
}) => {
  const [llmQuery, setLlmQuery] = useState("");
  const [llmResult, setLlmResult] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const message = event.data;
      if (message.type === 'llmQueryResult') {
        setLlmResult(message.sql);
        onChange?.(message.sql);
        setIsLoading(false);
      } else if (message.type === 'llmQueryError') {
        setLlmResult(`错误: ${message.error}`);
        setIsLoading(false);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [onChange]);

  const handleQueryLLM = () => {
    if (!llmQuery.trim()) {
      setLlmResult('请输入查询内容');
      return;
    }
    
    setIsLoading(true);
    setLlmResult('正在生成 SQL...');
    
    const vscode = (window as any).vscode;
    vscode.postMessage({
      type: "queryLLMAboutPgSql",
      query: llmQuery,
    });
  };

  return (
    <div className="db-query-renderer flex flex-col gap-2">
      <TextArea
        placeholder="请输入查询语句"
        resize="vertical"
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
      />

      <TextArea
        resize="vertical"
        placeholder="请输入需要查询的内容，大模型将自动将其转化为 SQL 语句"
        value={llmQuery}
        onChange={(e) => setLlmQuery(e.target.value)}
      />
      
      <div className="flex flex-col gap-2">
        <div className="text-sm text-gray-500">大模型查询结果</div>
        <div className="text-sm text-gray-500">
          {llmResult || "暂无返回内容"}
        </div>
      </div>

      <Button 
        onClick={handleQueryLLM}
        disabled={isLoading}
      >
        {isLoading ? '生成中...' : '生成 SQL'}
      </Button>
    </div>
  );
};
