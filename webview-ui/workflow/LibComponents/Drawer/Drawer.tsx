import * as React from "react";
import { Dialog } from "radix-ui";
import { Cross2Icon } from "@radix-ui/react-icons";
import clsx from "clsx";
import { twMerge } from "tailwind-merge";
import { type DrawerProps, type DrawerSide } from "./interface"; // 导入类型
import { Theme } from "@radix-ui/themes";

// ... (baseDrawerContentClasses 和 sideSpecificClasses 定义保持不变)
const baseDrawerContentClasses =
  "fixed z-50 gap-4 bg-white p-6 shadow-lg overflow-y-auto focus:outline-none \
   data-[state=open]:animate-in data-[state=closed]:animate-out \
   data-[state=closed]:duration-200 data-[state=open]:duration-250";

const sideSpecificClasses: Record<DrawerSide, string> = {
  // 使用 DrawerSide 类型
  top: "inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",
  bottom:
    "inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",
  left: "inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left",
  right:
    "inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right",
};

export const Drawer: React.FC<DrawerProps> = ({
  open,
  onOpenChange,
  children,
  title,
  description,
  trigger,
  side = "right",
  className,
}) => {
  const contentClasses = twMerge(
    "flex flex-col h-full",
    clsx(
      baseDrawerContentClasses,
      sideSpecificClasses[side] || sideSpecificClasses.right,
      className
    )
  );

  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      {trigger && <Dialog.Trigger asChild>{trigger}</Dialog.Trigger>}
      <Dialog.Portal>
        <Dialog.Overlay
          className={twMerge(
            clsx(
              "fixed inset-0 z-40 bg-black/50 backdrop-blur-sm",
              "data-[state=open]:animate-overlayShow data-[state=closed]:animate-overlayHide"
            )
          )}
        />
        <Dialog.Content
          className={contentClasses}
          style={{ height: '100%' }}
          onOpenAutoFocus={(event) => event.preventDefault()}
        >
          <Dialog.Title className="text-lg font-semibold text-gray-900">
            {title}
          </Dialog.Title>
          <div className="mt-4 flex-1 overflow-y-auto">
            <Theme style={{ height: '100%' }}>{children}</Theme>
          </div>
          <Dialog.Close asChild>
            <button
              type="button"
              className={twMerge(
                clsx(
                  "absolute top-4 right-4 rounded-sm opacity-70 transition-opacity hover:opacity-100",
                  "focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-offset-2",
                  "disabled:pointer-events-none data-[state=open]:bg-gray-100"
                )
              )}
              aria-label="Close"
            >
              <Cross2Icon className="h-5 w-5 text-gray-700" />
            </button>
          </Dialog.Close>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
};
