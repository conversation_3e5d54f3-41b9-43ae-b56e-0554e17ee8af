import fs from 'fs/promises'
import path from 'path'
import { Logger } from './logger'

export interface DirectoryItem {
  name: string
  type: 'file' | 'directory'
  path: string
  size?: number
  children?: DirectoryItem[]
}

function buildTreeStr(items: DirectoryItem[], prefix: string = '', isLast: boolean = true, output?: (response: string) => void,
  logger?: Logger): string {
  let treeStr = ''
  
  // Split items into directories and files
  const dirs = items.filter(item => item.type === 'directory')
  const files = items.filter(item => item.type === 'file')
  
  // Process directories first
  for (let i = 0; i < dirs.length; i++) {
    const item = dirs[i]
    const isLastItem = i === dirs.length - 1 && files.length === 0
    const connector = isLastItem ? '└──' : '├──'
    treeStr += `${prefix}${connector} ${item.name}/\n`
    
    // For directories, show count of contents
    if (item.children) {
      const childDirs = item.children.filter(c => c.type === 'directory').length
      const childFiles = item.children.filter(c => c.type === 'file').length
      const nextPrefix = prefix + (isLastItem ? '    ' : '│   ')
      
      if (childDirs > 0 || childFiles > 0) {
        const summary = []
        if (childDirs > 0) {
          summary.push(`${childDirs} director${childDirs === 1 ? 'y' : 'ies'}`)
        }
        if (childFiles > 0) {
          summary.push(`${childFiles} file${childFiles === 1 ? '' : 's'}`)
        }
        treeStr += `${nextPrefix}└── [${summary.join(', ')}]\n`
      }
    }
  }
  
  // Then process files
  if (files.length > 0) {
    for (let i = 0; i < Math.min(files.length, 10); i++) {
      const item = files[i]
      const isLastItem = i === Math.min(files.length, 10) - 1
      const connector = isLastItem ? '└──' : '├──'
      const sizeStr = item.size && item.size > 0 ? ` (${formatFileSize(item.size)})` : ''
      treeStr += `${prefix}${connector} ${item.name}${sizeStr}\n`
    }
    
    // If there are more than 10 files, show ellipsis
    if (files.length > 10) {
      treeStr += `${prefix}└── ... (${files.length - 10} more files)\n`
    }
  }
  
  return treeStr
}

export async function listDir(relativeWorkspacePath: string): Promise<[boolean, string]> {
  try {
    const dirPath = path.resolve(relativeWorkspacePath)
    
    // Check if directory exists
    try {
      const stats = await fs.stat(dirPath)
      if (!stats.isDirectory()) {
        return [false, `Error: Path is not a directory: ${relativeWorkspacePath}`]
      }
    } catch {
      return [false, `Error: Directory does not exist: ${relativeWorkspacePath}`]
    }

    const entries = await fs.readdir(dirPath, { withFileTypes: true })
    const items: DirectoryItem[] = []

    for (const entry of entries) {
      const itemPath = path.join(dirPath, entry.name)
      const relativePath = path.relative(process.cwd(), itemPath)
      
      let size: number | undefined
      if (entry.isFile()) {
        try {
          const stats = await fs.stat(itemPath)
          size = stats.size
        } catch {
          size = 0
        }
      }

      items.push({
        name: entry.name,
        type: entry.isDirectory() ? 'directory' : 'file',
        path: relativePath,
        size
      })
    }

    // Sort: directories first, then files, both alphabetically
    items.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'directory' ? -1 : 1
      }
      return a.name.localeCompare(b.name)
    })

    // Build tree string representation
    let treeStr = `${relativeWorkspacePath}/\n`
    
    // If directory is empty, just show the directory name
    if (items.length === 0) {
      return [true, treeStr]
    }
    
    // Add the contents
    treeStr += buildTreeStr(items)

    
    return [true, treeStr]
  } catch (error) {
    return [false, `Error listing directory: ${error}`]
  }
}

/**
 * Format file size to human readable format
 * @param bytes - File size in bytes
 * @returns Formatted size string (e.g., "512B", "1.5KB", "2.3MB")
 */
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0B'
  
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  const k = 1024
  const decimals = 1
  
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  const size = bytes / Math.pow(k, i)
  
  // For bytes, don't show decimals
  if (i === 0) {
    return `${bytes} B`
  }
  return `${size.toFixed(decimals)} ${units[i]}`
}
