import React, { useMemo } from "react";
import { WorkflowNodeOutputFieldProps } from "./interface";
import { Box, Flex, Tooltip, IconButton } from "@radix-ui/themes";
import { QuestionMarkCircledIcon } from "@radix-ui/react-icons";
import { WorkflowNodeTypeEnum } from "@lvjw/code-workflow-types"; 
import {
  buildCascaderData,
  buildGraph,
  getPreviousNodes,
} from "../WorkflowNodeInputs/helpers";
import { useAtom } from "jotai";
import { edgesAtom, nodesAtom, useNodeOutputsState } from "../WorkflowCanvas";
import Cascader from "../../LibComponents/Cascader";

export const WorkflowNodeOutputFieldComp: React.FC<
  WorkflowNodeOutputFieldProps
> = (props) => {
  const { attrName, label, desc, isDynamic, value, valueType } = props.output;

  // 从 jotai 获取数据
  const [nodes] = useAtom(nodesAtom);
  const [edges] = useAtom(edgesAtom);

  const {updateOutput}  = useNodeOutputsState(props.node.id)

  // 构建图并获取前置节点
  const graph = useMemo(() => buildGraph(nodes, edges), [nodes, edges]);
  const previousNodeIds = useMemo(
    () => getPreviousNodes(graph, props.node.id),
    [graph, props.node.id]
  );

  // 构建 Cascader 数据
  const cascaderData = useMemo(
    () => buildCascaderData(nodes, previousNodeIds),
    [nodes, previousNodeIds]
  );

  const handleCascaderChange = (selected: { path: string[]; label: string , labelPath: string[]}) => {
    // 这里可以更新 input 的 valuePath
    updateOutput(props.output.id, {
      ...props.output,
      valuePath: selected.path,
      valueLabelPath: selected.labelPath
    })
  };

  return (
    <>
      <Box position="relative">
        <Flex justify="between" className="nodrag cursor-default">
          <Flex align="center" gap="1" className="mb-1.5">
            <Box className="font-gray-600">{label || attrName}</Box>
            {desc && (
              <Tooltip content={desc}>
                <IconButton variant="ghost" radius="full">
                  <QuestionMarkCircledIcon color="#666" />
                </IconButton>
              </Tooltip>
            )}
            <Box
              style={{ lineHeight: "16px" }}
              className="text-gray-600 bg-gray-200 px-1 h-4 rounded-sm text-xs"
            >
              动态
            </Box>
            <Box
              style={{ lineHeight: "16px" }}
              className="text-gray-600 bg-gray-200 px-1 h-4 rounded-sm text-xs"
            >
              {valueType}
            </Box>
          </Flex>
        </Flex>
        {props.node.type === WorkflowNodeTypeEnum.workflowEnd && (
          <Cascader
            data={cascaderData}
            defaultSelectedPath={props.output.valuePath}
            defaultSelectedLabelPath={props.output.valueLabelPath}
            triggerLabel="选择属性..."
            onChange={handleCascaderChange}
          />
        )}
      </Box>
    </>
  );
};
