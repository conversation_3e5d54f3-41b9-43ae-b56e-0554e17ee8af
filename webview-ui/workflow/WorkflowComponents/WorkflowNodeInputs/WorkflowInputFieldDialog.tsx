import React, { useState, useCallback, useMemo } from "react";
import {
  Dialog,
  Flex,
  Text,
  TextField,
  Button,
  IconButton,
  Select,
  Switch,
  Box,
} from "@radix-ui/themes";
import { WorkflowInputFieldDialogProps, WorkflowNodeInputField } from "./interface";
import { GearIcon } from "@radix-ui/react-icons";

export const WorkflowInputFieldDialog: React.FC<WorkflowInputFieldDialogProps> = ({ 
  input,
  onSave 
}) => {
  // 初始化表单状态
  const [form, setForm] = useState<WorkflowNodeInputField>(() => ({
    ...input,
    isDynamic: input?.isDynamic ?? false,
    valueType: input?.valueType ?? 'string',
    attrName: input?.attrName ?? '',
    desc: input?.desc ?? ''
  }));

  // 使用 useCallback 优化事件处理函数
  const onFieldChange = useCallback((field: keyof WorkflowNodeInputField, value: any) => {
    setForm(prev => ({
      ...prev,
      [field]: value,
    }));
  }, []);

  // 处理保存
  const handleSave = useCallback(async () => {
    try {
      await onSave?.(form);
    } catch (error) {
      console.error('保存失败:', error);
      // 这里可以添加错误提示
    }
  }, [form, onSave]);

  // 使用 useMemo 优化选项列表
  // TODO处理那整个类型
  const typeOptions = useMemo(() => [
    { value: 'string', label: '字符串' },
    { value: 'number', label: '数字' },
    { value: 'boolean', label: '布尔' },
    { value: 'code', label: '代码' }
  ], []);

  return (
    <Dialog.Root>
      <Dialog.Trigger>
        <IconButton disabled={!input.editable}   color={input.removable ? "blue" : "gray"} className="cursor-pointer" variant="ghost" radius="full">
          <GearIcon />
        </IconButton>
      </Dialog.Trigger>
      <Dialog.Content maxWidth="450px">
        <Dialog.Title>编辑输入</Dialog.Title>

        <Flex direction="column" gap="3">
          <Flex align="center" gap="3">
            <Text className="w-8" as="div" size="2" mb="1" weight="bold">
              名称
            </Text>
            <TextField.Root
              className="flex-1"
              value={form.attrName}
              placeholder="请输入名称"
              onChange={(e) => onFieldChange("attrName", e.target.value)}
            />
          </Flex>
          <Flex align="center" gap="3">
            <Text className="w-8" as="div" size="2" mb="1" weight="bold">
              展示
            </Text>
            <TextField.Root
              className="flex-1"
              value={form.label}
              placeholder="请输入展示名称"
              onChange={(e) => onFieldChange("label", e.target.value)}
            />
          </Flex>

          <Flex align="center" gap="3">
            <Text className="w-8" as="div" size="2" mb="1" weight="bold">
              动态
            </Text>
            <Switch
              checked={form.isDynamic}
              onCheckedChange={(checked) => onFieldChange("isDynamic", checked)}
            />
          </Flex>

          <Flex align="center" gap="3">
            <Text className="w-8" as="div" size="2" mb="1" weight="bold">
              类型
            </Text> 
            <Select.Root
              value={form.valueType}
              onValueChange={(value) => onFieldChange("valueType", value)}
            >
              <Select.Trigger className="flex-1" />
              <Select.Content className="w-full">
                {typeOptions.map(option => (
                  <Select.Item key={option.value} value={option.value}>
                    {option.label}
                  </Select.Item>
                ))}
              </Select.Content>
            </Select.Root>
          </Flex>

          <Flex align="center" gap="3">
            <Text className="w-8" as="div" size="2" mb="1" weight="bold">
              描述
            </Text>
            <TextField.Root
              className="flex-1"
              value={form.desc}
              placeholder="请输入描述"
              onChange={(e) => onFieldChange("desc", e.target.value)}
            />
          </Flex>
        </Flex>

        <Flex gap="3" mt="4" justify="end">
          <Dialog.Close>
            <Button variant="soft" color="gray">
              取消
            </Button>
          </Dialog.Close>
          <Dialog.Close>
            <Button onClick={handleSave}>保存</Button>
          </Dialog.Close>
        </Flex>
      </Dialog.Content>
    </Dialog.Root>
  );
};