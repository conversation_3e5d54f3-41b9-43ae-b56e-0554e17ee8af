import OpenAI from 'openai'

export async function callLlm(prompts: any[]): Promise<string> {
    const model = process.env.LLM_MODEL_NAME
    const apiKey = process.env.LLM_API_KEY
    const baseURL = process.env.LLM_BASE_URL

    if (!model) {
        throw new Error('LLM_MODEL_NAME environment variable is not set')
    }

    if (!apiKey) {
        throw new Error('LLM_API_KEY environment variable is not set')
    }

    if (!baseURL) {
        throw new Error('LLM_BASE_URL environment variable is not set')
    }
    const client = new OpenAI({ apiKey, baseURL })

    try {
        const response = await client.chat.completions.create({
            model,
            messages: prompts,
        })
        const responseText = response.choices[0].message.content || ''

        return responseText
    } catch (e) {
        console.error('Error creating OpenAI client:', e)
        return JSON.stringify({
            tool: 'tryToFinish',
            reason: 'LLM call failed：' + (e instanceof Error ? e.message : e?.toString()),
            params: {},
        })
    }
}
