{"name": "dev-expert", "displayName": "dev-expert", "description": "a new extension", "version": "0.0.5", "publisher": "lv<PERSON><PERSON><PERSON>", "engines": {"vscode": "^1.82.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "repository": {"type": "git", "url": "https://github.com/lambda-inc-com/ai-code-full/tree/main/packages/dev-expert"}, "main": "./dist-extension/extension/extension.js", "contributes": {"configuration": {"title": "Flow Editor", "properties": {"flowEditor.openaiApiKey": {"type": "string", "default": "", "description": "OpenAI API 密钥"}}}, "viewsContainers": {"activitybar": [{"id": "aiCodingAssistant", "title": "AI编程助手", "icon": "$(robot)"}]}, "views": {"explorer": [{"id": "flowTreeView", "name": "Workflows"}, {"id": "databaseConnections", "name": "数据库", "icon": "$(database)"}], "aiCodingAssistant": [{"id": "aiChatView", "name": "AI聊天", "type": "webview"}]}, "menus": {"view/title": [{"command": "dev-expert.refreshConnection", "when": "view == databaseConnections", "group": "navigation"}, {"command": "dev-expert.addConnection", "when": "view == databaseConnections", "group": "navigation"}, {"command": "dev-expert.openQueryConsole", "when": "view == databaseConnections", "group": "navigation"}], "view/item/context": [{"command": "dev-expert.removeConnection", "when": "view == databaseConnections && viewItem == connection", "group": "inline"}, {"command": "dev-expert.showSchemaERDiagram", "when": "view == databaseConnections && viewItem == schema", "group": "inline"}]}, "commands": [{"command": "dev-expert.addConnection", "title": "新建连接", "icon": "$(add)"}, {"command": "dev-expert.removeConnection", "title": "删除连接", "icon": "$(trash)"}, {"command": "dev-expert.refreshConnection", "title": "刷新", "icon": "$(refresh)"}, {"command": "dev-expert.showTableData", "title": "显示表数据", "icon": "$(table)"}, {"command": "dev-expert.openQueryConsole", "title": "查询控制台", "icon": "$(terminal-cmd)"}, {"command": "dev-expert.showApiDocs", "title": "查看 API 文档", "icon": "$(book)"}, {"command": "dev-expert.showSchemaERDiagram", "title": "查看 ER 图", "icon": "$(database)"}, {"command": "dev-expert.showUserProfile", "title": "用户详情页", "icon": "$(user)"}, {"command": "dev-expert.logout", "title": "退出登录", "icon": "$(sign-out)"}, {"command": "dev-expert.openAIChat", "title": "打开AI聊天", "icon": "$(comment-discussion)"}]}, "webview": {"localResourceRoots": ["${extensionUri}/resources"]}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "dev": "npm run clean && node ./scripts/dev.js", "package": "node ./scripts/package.js", "dev:webview": "vite .", "build": "npm run clean && npm run build:webview && npm run build:extension", "build:webview": "vite build", "build:extension": "tsc -p ./extension/tsconfig.json"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@lvjw/code-workflow-types": "^1.0.0", "@modelcontextprotocol/sdk": "^1.12.1", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/themes": "^3.2.1", "@xyflow/react": "^12.6.0", "ai": "^4.3.16", "axios": "^1.9.0", "chokidar": "^4.0.3", "clsx": "^2.1.1", "dotenv": "^16.5.0", "exceljs": "^4.4.0", "fast-deep-equal": "^3.1.3", "iconv-lite": "^0.6.3", "isbinaryfile": "^5.0.4", "jotai": "^2.12.4", "jschardet": "^3.1.4", "mammoth": "^1.9.1", "monaco-editor": "^0.45.0", "openai": "^4.104.0", "pdf-parse": "^1.1.1", "pg": "^8.16.0", "pocketflow": "^1.0.4", "radix-ui": "^1.4.1", "react": "^19.1.0", "react-dom": "^19.1.0", "reconnecting-eventsource": "^1.6.4", "tailwind-merge": "^3.3.0", "typescript": "^5.8.3", "uuid": "^11.1.0", "vsce": "^2.15.0"}, "devDependencies": {"@tailwindcss/vite": "^4.1.6", "@types/mocha": "^10.0.10", "@types/node": "~20.17.30", "@types/pdf-parse": "^1.1.5", "@types/pg": "^8.15.2", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/vscode": "^1.82.0", "@vitejs/plugin-react": "^4.4.0", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.5.2", "concurrently": "^8.2.2", "globals": "^16.0.0", "p-wait-for": "^5.0.2", "prettier-plugin-tailwindcss": "^0.6.12", "rimraf": "^5.0.5", "tailwindcss": "^4.1.6", "vite": "^6.3.2"}, "packageManager": "npm@9.0.0"}