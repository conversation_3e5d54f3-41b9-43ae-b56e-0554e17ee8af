import * as vscode from "vscode";
import { ConnectionManager } from "../utils/connection/connection-manager";
import { DatabaseTreeDataProvider } from "../TreeViews/DatabaseTreeDataProvider";

export class ConnectionWebviewContainer {
  private panels: Map<string, vscode.WebviewPanel> = new Map();
  private connectionManager: ConnectionManager;
  private treeDataProvider: DatabaseTreeDataProvider;

  constructor(
    private readonly context: vscode.ExtensionContext,
    connectionManager: ConnectionManager,
    treeDataProvider: DatabaseTreeDataProvider
  ) {
    this.connectionManager = connectionManager;
    this.treeDataProvider = treeDataProvider;
  }

  openConnectionWebviewPanel() {
    const key = "connection-form";

    // 已有则激活
    if (this.panels.has(key)) {
      this.panels.get(key)!.reveal();
      return;
    }

    // 新建
    const panel = this.createPanel(this.context, "新建连接");
    this.panels.set(key, panel);

    // 关闭时移除
    panel.onDidDispose(() => {
      this.panels.delete(key);
    });
  }

  createPanel(context: vscode.ExtensionContext, title: string) {
    const panel = vscode.window.createWebviewPanel(
      "connectionView",
      title,
      vscode.ViewColumn.One,
      {
        // Enable JavaScript in the webview
        enableScripts: true,
        // Restrict the webview to only load resources from the extension's directory
        localResourceRoots: [
          vscode.Uri.joinPath(context.extensionUri, "dist", "workflow"),
        ],
        // Retain the webview when it's not visible
        retainContextWhenHidden: true,
      }
    );

    // 处理来自WebView的消息
    panel.webview.onDidReceiveMessage(
      async (message) => {
        console.log(`收到WebView消息: ${JSON.stringify(message)}`);

        if (message.command === "saveConnection") {
          console.log(`保存连接数据: ${JSON.stringify(message.connection)}`);

          try {
            // 先保存连接数据到变量
            const result = await this.connectionManager.addConnection(
              message.connection
            );
            console.log(`保存连接结果: ${JSON.stringify(result)}`);
            // 刷新数据库 treeview
            this.treeDataProvider.refresh();
            panel.webview.postMessage({
              command: "connectionResult",
              success: true,
              message: "连接成功",
            });
          } catch (e) {
            console.error(`保存连接失败: ${e}`);
            panel.webview.postMessage({
              command: "connectionResult",
              success: false,
              message: "连接失败",
            });
          }
        } else if (message.command === "testConnection") {
          console.log(`测试连接数据: ${JSON.stringify(message.connection)}`);

          // 测试连接
          try {
            const result = await this.connectionManager.testConnection(
              message.connection
            );
            console.log(`测试连接结果: ${JSON.stringify(result)}`);
            panel.webview.postMessage({
              command: "testConnectionResult",
              success: true,
              message: "连接成功",
            });
          } catch (e) {
            panel.webview.postMessage({
              command: "testConnectionResult",
              success: false,
              message: "连接失败",
            });
          }
        } else if (message.command === "cancelConnection") {
          console.log("用户取消连接");
          // 然后关闭面板
          setTimeout(() => {
            panel?.dispose();
            console.log("面板已关闭（取消后）");
          }, 100);
        }
      },
      undefined,
      this.context.subscriptions
    );

    panel.webview.html = this.getFormHtml();

    return panel;
  }

  /**
   * 生成表单HTML
   */
  private getFormHtml(): string {
    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'unsafe-inline';">
<title>数据库连接</title>
<style>
    :root {
        --container-padding: 20px;
        --input-padding-vertical: 6px;
        --input-padding-horizontal: 4px;
        --input-margin-vertical: 4px;
        --input-margin-horizontal: 0;
    }

    body {
        padding: 0 var(--container-padding);
        color: var(--vscode-foreground);
        font-size: var(--vscode-font-size);
        font-weight: var(--vscode-font-weight);
        font-family: var(--vscode-font-family);
        background-color: var(--vscode-editor-background);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    ol,
    ul {
        padding-left: var(--container-padding);
    }

    body > *,
    form > * {
        margin-block-start: var(--input-margin-vertical);
        margin-block-end: var(--input-margin-vertical);
    }

    *:focus {
        outline-color: var(--vscode-focusBorder) !important;
    }

    a {
        color: var(--vscode-textLink-foreground);
    }

    a:hover,
    a:active {
        color: var(--vscode-textLink-activeForeground);
    }

    code {
        font-size: var(--vscode-editor-font-size);
        font-family: var(--vscode-editor-font-family);
    }

    button {
        border: none;
        padding: var(--input-padding-vertical) var(--input-padding-horizontal);
        width: 100%;
        text-align: center;
        outline: 1px solid transparent;
        outline-offset: 2px !important;
        color: var(--vscode-button-foreground);
        background: var(--vscode-button-background);
        cursor: pointer;
        margin-top: 10px;
        border-radius: 4px;
    }

    button:hover {
        background: var(--vscode-button-hoverBackground);
    }

    button:focus {
        outline-color: var(--vscode-focusBorder);
    }

    button.secondary {
        color: var(--vscode-button-secondaryForeground);
        background: var(--vscode-button-secondaryBackground);
    }

    button.secondary:hover {
        background: var(--vscode-button-secondaryHoverBackground);
    }

    input:not([type='checkbox']),
    textarea,
    select {
        display: block;
        width: 100%;
        max-width: 400px;
        border: none;
        font-family: var(--vscode-font-family);
        padding: var(--input-padding-vertical) var(--input-padding-horizontal);
        color: var(--vscode-input-foreground);
        outline-color: var(--vscode-input-border);
        background-color: var(--vscode-input-background);
        margin-bottom: 10px;
    }

    input::placeholder,
    textarea::placeholder {
        color: var(--vscode-input-placeholderForeground);
    }

    .form-section {
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--vscode-panel-border);
    }

    .form-section-title {
        font-weight: bold;
        margin-bottom: 10px;
        color: var(--vscode-descriptionForeground);
    }

    .form-row {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 15px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }
    .form-row label {
        flex: 0 0 120px;
        margin-bottom: 0;
        text-align: left;
        padding-right: 4px;
        color: var(--vscode-foreground);
        font-weight: 500;
    }
    .form-row input,
    .form-row select {
        flex: 1;
        margin-bottom: 0;
        min-width: 0;
    }
    .form-group { display: none; } /* 兼容旧结构，隐藏 */

    .button-container {
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        max-width: 400px;
        width: 100%;
        gap: 10px;
    }

    .button-container button {
        width: 48%;
        max-width: 190px;
    }

    #connectionForm {
        width: 100%;
        max-width: 600px;
    }

    h1 {
        font-size: 1.5em;
        margin-bottom: 20px;
        border-bottom: 1px solid var(--vscode-panel-border);
        padding-bottom: 10px;
    }

    #status-message {
        margin-top: 10px;
        padding: 8px;
        border-radius: 4px;
        display: none;
    }

    #status-message.success {
        background-color: rgba(0, 128, 0, 0.2);
        color: var(--vscode-terminal-ansiGreen);
    }

    #status-message.error {
        background-color: rgba(255, 0, 0, 0.2);
        color: var(--vscode-terminal-ansiRed);
    }
</style>
</head>
<body>
<h1>添加数据库连接</h1>
<form id="connectionForm">
    <div class="form-section">
        <div class="form-section-title">基本信息</div>
        <div class="form-row">
            <label for="connectionName">连接名称 *</label>
            <input type="text" id="connectionName" name="connectionName" placeholder="我的数据库连接">
        </div>
        <div class="form-row">
            <label for="connectionType">数据库类型 *</label>
            <select disabled id="connectionType" name="connectionType">
                <option value="PostgreSQL" selected>PostgreSQL</option>
            </select>
        </div>
    </div>
    <div class="form-section">
        <div class="form-section-title">连接详情</div>
        <div class="form-row">
            <label for="host">主机地址</label>
            <input placeholder="请输入主机地址" type="text" id="host">
        </div>
        <div class="form-row">
            <label for="port">端口</label>
            <input placeholder="请输入端口" type="number" id="port" name="port">
        </div>
        <div class="form-row">
            <label for="database">数据库名称</label>
            <input placeholder="请输入数据库名称" type="text" id="database" name="database">
        </div>
    </div>
    <div class="form-section">
        <div class="form-section-title">认证信息</div>
        <div class="form-row">
            <label for="username">用户名</label>
            <input placeholder="请输入用户名" type="text" id="username" name="username">
        </div>
        <div class="form-row">
            <label for="password">密码</label>
            <input placeholder="请输入密码" type="password" id="password" name="password">
        </div>
    </div>

    <div id="status-message"></div>

    <div class="button-container">
        <button type="button" id="cancelButton" class="secondary">取消</button>
        <button type="button" id="testButton" class="secondary">测试连接</button>
        <button type="submit" id="saveButton">保存连接</button>
    </div>
</form>

<script>
    (function() {
        const vscode = acquireVsCodeApi();
        const connectionTypeSelect = document.getElementById('connectionType');
        const connectionForm = document.getElementById('connectionForm');
        const cancelButton = document.getElementById('cancelButton');
        const testButton = document.getElementById('testButton');
        const statusMessage = document.getElementById('status-message');

        // 显示状态消息
        function showStatus(message, isError = false) {
            statusMessage.textContent = message;
            statusMessage.style.display = 'block';

            if (isError) {
                statusMessage.className = 'error';
            } else {
                statusMessage.className = 'success';
            }
        }

        // 隐藏状态消息
        function hideStatus() {
            statusMessage.style.display = 'none';
        }

        // 根据选择的数据库类型设置默认端口
        connectionTypeSelect.addEventListener('change', function() {
            const selectedType = this.value;
            const portInput = document.getElementById('port');
            // 只保留 PostgreSQL，直接设置默认端口
            portInput.placeholder = '5432';
        });

        // 处理表单提交
        connectionForm.addEventListener('submit', function(e) {
            e.preventDefault();
            hideStatus();

            // 收集所有表单字段
            const connectionType = connectionTypeSelect.value;
            const connectionName = document.getElementById('connectionName').value.trim();
            const host = document.getElementById('host').value.trim();
            const port = document.getElementById('port').value.trim();
            const database = document.getElementById('database').value.trim();
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;

            // 所有字段必填校验
            if (!connectionName) {
                showStatus('请输入连接名称', true);
                return;
            }
            if (!connectionType) {
                showStatus('请选择数据库类型', true);
                return;
            }
            if (!host) {
                showStatus('请输入主机地址', true);
                return;
            }
            if (!port) {
                showStatus('请输入端口', true);
                return;
            }
            if (!database) {
                showStatus('请输入数据库名称', true);
                return;
            }
            if (!username) {
                showStatus('请输入用户名', true);
                return;
            }
            if (!password) {
                showStatus('请输入密码', true);
                return;
            }

            // 创建连接数据
            let connectionData = {
                name: connectionName,
                type: connectionType,
                host,
                port: port ? parseInt(port, 10) : undefined,
                database,
                username,
                password
            };

            // 禁用按钮，防止重复提交，进入 loading 状态
            document.getElementById('saveButton').disabled = true;
            document.getElementById('saveButton').textContent = '正在连接数据库...';
            document.getElementById('cancelButton').disabled = true;

            // 显示 loading 状态
            showStatus('正在连接数据库...');

            // 发送数据到VSCode
            try {
                vscode.postMessage({
                    command: 'saveConnection',
                    connection: connectionData
                });
            } catch (error) {
                console.error('发送数据失败:', error);
                showStatus('发送数据失败: ' + error, true);
                document.getElementById('saveButton').disabled = false;
                document.getElementById('saveButton').textContent = '保存连接';
                document.getElementById('cancelButton').disabled = false;
            }
        });

        // 测试连接按钮逻辑
        testButton.addEventListener('click', function() {
            hideStatus();
            // 收集所有表单字段
            const connectionType = connectionTypeSelect.value;
            const connectionName = document.getElementById('connectionName').value.trim();
            const host = document.getElementById('host').value.trim();
            const port = document.getElementById('port').value.trim();
            const database = document.getElementById('database').value.trim();
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;

            // 所有字段必填校验
            if (!connectionName) {
                showStatus('请输入连接名称', true);
                return;
            }
            if (!connectionType) {
                showStatus('请选择数据库类型', true);
                return;
            }
            if (!host) {
                showStatus('请输入主机地址', true);
                return;
            }
            if (!port) {
                showStatus('请输入端口', true);
                return;
            }
            if (!database) {
                showStatus('请输入数据库名称', true);
                return;
            }
            if (!username) {
                showStatus('请输入用户名', true);
                return;
            }
            if (!password) {
                showStatus('请输入密码', true);
                return;
            }

            // 创建连接数据
            let connectionData = {
                name: connectionName,
                type: connectionType,
                host,
                port: port ? parseInt(port, 10) : undefined,
                database,
                username,
                password
            };

            // 按钮进入 loading 状态
            testButton.disabled = true;
            testButton.textContent = '正在测试...';
            document.getElementById('saveButton').disabled = true;
            document.getElementById('cancelButton').disabled = true;
            showStatus('正在测试数据库连接...');

            // 发送测试连接消息
            try {
                vscode.postMessage({
                    command: 'testConnection',
                    connection: connectionData
                });
            } catch (error) {
                showStatus('发送测试请求失败: ' + error, true);
                testButton.disabled = false;
                testButton.textContent = '测试连接';
                document.getElementById('saveButton').disabled = false;
                document.getElementById('cancelButton').disabled = false;
            }
        });

        // 监听后端返回的连接结果（测试和保存共用）
        window.addEventListener('message', event => {
            const message = event.data;
            if (message.command === 'connectionResult') {
                if (message.success) {
                    showStatus('创建成功！', false);
                    // 恢复按钮状态
                    document.getElementById('saveButton').disabled = false;
                    document.getElementById('saveButton').textContent = '保存连接';
                    document.getElementById('cancelButton').disabled = false;
                    testButton.disabled = false;
                    testButton.textContent = '测试连接';
                    // 只有保存连接时才关闭页面
                    if (document.getElementById('saveButton').textContent === '保存连接') {
                        setTimeout(() => {
                            vscode.postMessage({ command: 'closePanel' });
                        }, 800);
                    }
                } else {
                    showStatus('连接失败：' + (message.message || '未知错误'), true);
                    testButton.disabled = false;
                    testButton.textContent = '测试连接';
                    document.getElementById('saveButton').disabled = false;
                    document.getElementById('saveButton').textContent = '保存连接';
                    document.getElementById('cancelButton').disabled = false;
                }
            } else if (message.command === 'testConnectionResult') {
                if (message.success) {
                    showStatus('连接成功！', false);
                    // 恢复按钮状态
                    document.getElementById('saveButton').disabled = false;
                    document.getElementById('saveButton').textContent = '保存连接';
                    document.getElementById('cancelButton').disabled = false;
                    testButton.disabled = false;
                    testButton.textContent = '测试连接';
                } else {
                    showStatus('连接失败：' + (message.message || '未知错误'), true);
                    // 恢复按钮状态
                    document.getElementById('saveButton').disabled = false;
                    document.getElementById('saveButton').textContent = '保存连接';
                    document.getElementById('cancelButton').disabled = false;
                    testButton.disabled = false;
                    testButton.textContent = '测试连接';
                }
            }
        });

        // 处理取消按钮
        cancelButton.addEventListener('click', function() {
            vscode.postMessage({
                command: 'cancelConnection'
            });
        });
    }());
</script>
</body>
</html>`;
  }
}
