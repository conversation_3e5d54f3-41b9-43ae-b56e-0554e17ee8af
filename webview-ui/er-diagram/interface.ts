export interface Column {
    id?: string;
    // 基本信息
    name: string; // 列名
    comment: string; // 列注释
    type: string; // 数据类型
    isPrimaryKey: boolean; // 是否为主键

    // TypeORM @Column 选项
    isNullable?: boolean; // 是否可为空
    isUnique?: boolean; // 是否唯一
    isGenerated?: boolean; // 是否自动生成
    default?: any; // 默认值
    length?: number; // 字符串长度
    precision?: number; // 数字精度
    scale?: number; // 小数位数
    charset?: string; // 字符集
    collation?: string; // 排序规则
    enum?: any[]; // 枚举值列表
    array?: boolean; // 是否为数组

    isForeignKey?: boolean; // 是否为外键
    references?: {
        table: string; // 引用的表名
        column: string; // 引用的列名
    };

    // class-validator 验证规则
    validations?: {
        isEmail?: boolean; // 是否为邮箱
        isUrl?: boolean; // 是否为URL
        isDate?: boolean; // 是否为日期
        min?: number; // 最小值
        max?: number; // 最大值
        minLength?: number; // 最小长度
        maxLength?: number; // 最大长度
        isInt?: boolean; // 是否为整数
        isNumber?: boolean; // 是否为数字
        isBoolean?: boolean; // 是否为布尔值
        matches?: string; // 正则表达式匹配
        isIn?: any[]; // 允许的值列表
        isNotEmpty?: boolean; // 是否不能为空
    };

    // 关系选项
    foreignKey?: {
        referencedTable: string; // 引用的表名
        referencedColumn: string; // 引用的列名
        onDelete?: string; // 删除时的操作
        onUpdate?: string; // 更新时的操作
    };
}

export interface Table {
    name: string;
    type: "table" | "view";
    columns: Column[];
    // Add id to Table interface if it's different from name or used for nodes
    id?: string;
}
export interface Schema {
    name: string;
    tables: Table[];
}

declare global {
    interface Window {
        webviewInfo?: {
            viewType: string;
            title: string;
            panelId: string;
        };
        vscode: any;
    }
}
