import { Flow } from 'pocketflow'
import { CodeAgentActionEnum, CodingAgentSharedStore } from './types'
import { MainDecisionAgentNode } from './nodes/mainDecisionAgent'
import { ReadFileActionNode } from './nodes/readFileAction'
import { DeleteFileActionNode } from './nodes/deleteFileAction'
import { GrepSearchActionNode } from './nodes/grepSearchAction'
import { ListDirActionNode } from './nodes/listDirAction'
import { ExecuteCommandActionNode } from './nodes/executeCommandAction'
import { ReadTargetFileNode } from './nodes/readTargetFile'
import { AnalyzeAndPlanChangesNode } from './nodes/analyzeAndPlanChanges'
import { ApplyChangesBatchNode } from './nodes/applyChangesBatch'
import { TryToFinishNode } from './nodes/tryToFinish'
import { UserMCPToolNode } from './nodes/useMCPTool'


export function createEditCodeAgentFlow(): Flow<CodingAgentSharedStore> {
  const readTargetFileNode = new ReadTargetFileNode()
  const analyzeAndPlanChangesNode = new AnalyzeAndPlanChangesNode()
  const applyChangesBatchNode = new ApplyChangesBatchNode()

  readTargetFileNode.next(analyzeAndPlanChangesNode)
  analyzeAndPlanChangesNode.next(applyChangesBatchNode)

  return new Flow<CodingAgentSharedStore>(readTargetFileNode)
}

export function createCodeAgengFlow(): Flow<CodingAgentSharedStore> {
  // Create nodes

  const mainAgentNode = new MainDecisionAgentNode()
  const readFileNode = new ReadFileActionNode()
  const deleteFileNode = new DeleteFileActionNode()
  const grepSearchNode = new GrepSearchActionNode()
  const listDirNode = new ListDirActionNode()
  const executeCommandNode = new ExecuteCommandActionNode()
  const tryToFinishNode = new TryToFinishNode()
  const editCodeAgentFlow = createEditCodeAgentFlow()
  const useMCPToolNode = new UserMCPToolNode()

  mainAgentNode.on(CodeAgentActionEnum.readFile, readFileNode)
  mainAgentNode.on(CodeAgentActionEnum.grepSearch, grepSearchNode)
  mainAgentNode.on(CodeAgentActionEnum.listDir, listDirNode)
  mainAgentNode.on(CodeAgentActionEnum.deleteFile, deleteFileNode)
  mainAgentNode.on(CodeAgentActionEnum.executeCommand, executeCommandNode)
  // TODO edit File
  mainAgentNode.on(CodeAgentActionEnum.editFile, editCodeAgentFlow)
  mainAgentNode.on(CodeAgentActionEnum.tryToFinish, tryToFinishNode)
  mainAgentNode.on(CodeAgentActionEnum.useMCPTool, useMCPToolNode)

  readFileNode.next(mainAgentNode)
  grepSearchNode.next(mainAgentNode)
  listDirNode.next(mainAgentNode)
  deleteFileNode.next(mainAgentNode)
  executeCommandNode.next(mainAgentNode)
  editCodeAgentFlow.next(mainAgentNode)
  useMCPToolNode.next(mainAgentNode)
  tryToFinishNode.on(CodeAgentActionEnum.userQuery, mainAgentNode)

  // Create flow starting with input node
  return new Flow<CodingAgentSharedStore>(mainAgentNode)
}