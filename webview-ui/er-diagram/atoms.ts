import { atom } from "jotai";
import {
  applyNodeChang<PERSON>,
  applyEdgeChanges,
  NodeChange,
  EdgeChange,
  Node,
  Edge,
} from "@xyflow/react";
import { Column, Schema, Table } from "./interface"; // Import Schema and Table
import { useAtom } from 'jotai'; // Ensure useAtom is imported if not already at the top

// Atom for ER diagram nodes
export const erNodesAtom = atom<Node[]>([]);

// Atom for ER diagram edges
export const erEdgesAtom = atom<Edge[]>([]);

// Atom to handle node changes
export const onErNodesChangeAtom = atom(
  null,
  (get, set, changes: NodeChange<Node>[]) => {
    const currentNodes = get(erNodesAtom);
    const nextNodes = applyNodeChanges(changes, currentNodes);
    set(erNodesAtom, nextNodes);
  }
);

// Atom to handle edge changes
export const onErEdgesChangeAtom = atom(
  null,
  (get, set, changes: EdgeChange<Edge>[]) => {
    const currentEdges = get(erEdges<PERSON>tom);
    const nextEdges = applyEdgeChanges(changes, currentEdges);
    set(erE<PERSON><PERSON>tom, nextEdges);
  }
);

// Write-only atom to trigger ER diagram generation
export const generateERDiagramAtom = atom(
  null, // First argument is the read part, null for write-only
  (get, set, schema: Schema) => {
    const currentNodesFromAtom = get(erNodesAtom);
    
    const existingNodeMap = new Map(
      currentNodesFromAtom.map((node) => [node.id, node])
    );

    const newNodesFromSchema: Node[] = [];
    const newEdges: Edge[] = [];
    let xOffset = 0;
    const yOffset = 0;
    const nodeSpacing = 300;

    if (currentNodesFromAtom.length > 0) {
      const nodesNotInSchema = currentNodesFromAtom.filter(n => !schema.tables.find(t => t.name === n.id));
      let maxXNode = null as Node | null;
      if (nodesNotInSchema.length > 0) {
        maxXNode = nodesNotInSchema.reduce((max, node) => 
          node.position.x > (max ? max.position.x : -Infinity) ? node : max, 
        null as Node | null);
      }
      
      if (maxXNode) {
        xOffset = maxXNode.position.x + nodeSpacing;
      } else {
        const maxExistingXInSchema = currentNodesFromAtom.reduce((max, node) => 
            (schema.tables.find(t => t.name === node.id) && node.position.x > max) ? node.position.x : max, 
            0
        );
        xOffset = maxExistingXInSchema > 0 ? maxExistingXInSchema + nodeSpacing : 0; 
      }
    }

    schema.tables.forEach((table) => {
      const existingNode = existingNodeMap.get(table.name);
      let position;
      if (existingNode) {
        position = existingNode.position;
      } else {
        position = { x: xOffset, y: yOffset };
        xOffset += nodeSpacing;
      }

      const tableNode: Node = {
        id: table.name + '-' + Date.now().toString() + '-' + Math.floor(Math.random() * 10000) + Math.floor(Math.random() * 10000),
        position: position,
        type: "erNode",
        data: table as any,
      };
      newNodesFromSchema.push(tableNode);

      table.columns.forEach((column) => {
        if (column.isForeignKey && column.references) {
          const edge: Edge = {
            id: `${table.name}-${column.name}-${column.references.table}-${column.references.column}`,
            source: table.name,
            target: column.references.table,
            label: `${column.name} -> ${column.references.column}`,
            type: "smoothstep",
            animated: true,
            style: { stroke: "#999" },
          };
          newEdges.push(edge);
        }
      });
    });

    const finalNodesMap = new Map(existingNodeMap);
    newNodesFromSchema.forEach(newNode => {
      const existingNode = finalNodesMap.get(newNode.id);
      if (existingNode) {
        finalNodesMap.set(newNode.id, { ...existingNode, data: { ...existingNode.data, ...newNode.data }, position: existingNode.position });
      } else {
        finalNodesMap.set(newNode.id, newNode);
      }
    });

    set(erNodesAtom, Array.from(finalNodesMap.values()));
    set(erEdgesAtom, newEdges);
  }
);

// Atom to get current nodes (read-only)
export const currentErNodesAtom = atom((get) => get(erNodesAtom));

// Atom to get current edges (read-only)
export const currentErEdgesAtom = atom((get) => get(erEdgesAtom));

// Atom to control the visibility of the table edit drawer
export const isTableEditDrawerOpenAtom = atom<boolean>(false);

// Atom to store the ID of the table currently being edited
export const editingTableIdAtom = atom<string | null>(null);

// Atom to update table name and comment
export const updateTableAtom = atom(
  null, // read-only part is null as this is a write-only atom
  (get, set, { id, name, comment, columns }: { id: string; name: string; comment: string, columns: Column[] }) => {
    const currentNodes = get(erNodesAtom);
    const updatedNodes = currentNodes.map(node => {
      if (node.id === id) {
        // Ensure we are preserving other data properties if they exist
        const existingTableData = node.data as unknown as Table; 
        return {
          ...node,
          data: {
            ...existingTableData, // Spread existing table data
            name: name,
            comment: comment,
            columns: columns ?? []
          },
        };
      }
      return node;
    });
    set(erNodesAtom, updatedNodes);
  }
);


// Custom hook to manage ER Diagram state and actions
export const useERDiagramState = () => {
  const [nodes, setNodes] = useAtom(erNodesAtom);
  const [edges, setEdges] = useAtom(erEdgesAtom);
  const [, onNodesChange] = useAtom(onErNodesChangeAtom);
  const [, onEdgesChange] = useAtom(onErEdgesChangeAtom);
  const [, triggerGenerateERDiagram] = useAtom(generateERDiagramAtom);

  return {
    nodes,
    setNodes, // Exposing setNodes directly might be too broad if only generateERDiagramAtom should modify it
    edges,
    setEdges, // Same consideration for setEdges
    onNodesChange,
    onEdgesChange,
    triggerGenerateERDiagram,
  };
};


// Custom hook to manage ER Diagram state and actions (more controlled)
export const useERDiagramStateControlled = () => {
  const [nodes] = useAtom(erNodesAtom); // Read-only access to nodes
  const [edges] = useAtom(erEdgesAtom); // Read-only access to edges
  const [, onNodesChange] = useAtom(onErNodesChangeAtom);
  const [, onEdgesChange] = useAtom(onErEdgesChangeAtom);
  const [, triggerGenerateERDiagram] = useAtom(generateERDiagramAtom);

  return {
    nodes,
    edges,
    onNodesChange,
    onEdgesChange,
    triggerGenerateERDiagram,
  };
};