import * as vscode from 'vscode'
import * as fs from 'fs/promises'
import chokidar, { FSWatcher } from 'chokidar'
import { setTimeout as setTimeoutPromise } from 'node:timers/promises'
import {
    McpConnection,
    McpResource,
    McpResourceResponse,
    McpResourceTemplate,
    McpServer,
    McpServerConfig,
    McpTool,
    McpToolCallResponse,
} from './types'
import path from 'node:path'
import { fileExistsAtPath } from '../../utils/fs'
import { z } from 'zod'
import { BaseConfigSchema, ServerConfigSchema, McpSettingsSchema } from './schemas'
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js'
import { SSEClientTransport } from '@modelcontextprotocol/sdk/client/sse.js'
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js'
import { Client } from '@modelcontextprotocol/sdk/client/index.js'
import { DEFAULT_MCP_TIMEOUT_SECONDS, DEFAULT_REQUEST_TIMEOUT_MS } from './constants'
import {
    CallToolResultSchema,
    ListResourcesResultSchema,
    ListResourceTemplatesResultSchema,
    ListToolsResultSchema,
    ReadResourceResultSchema,
} from '@modelcontextprotocol/sdk/types.js'
import deepEqual from 'fast-deep-equal'

export class McpHub {
    private getSettingsFilePath: () => Promise<string>
    private clientVersion: string
    private disposables: vscode.Disposable[] = []
    private settingsWatcher?: vscode.FileSystemWatcher
    private fileWatchers: Map<string, FSWatcher> = new Map()
    connections: McpConnection[] = []
    isConnecting: boolean = true

    constructor(getSettingsDirectoryPath: () => Promise<string>, clientVersion?: string) {
        this.getSettingsFilePath = getSettingsDirectoryPath
        this.clientVersion = clientVersion ?? '1.0.0'
        this.watchMcpSettingsFile()
        this.initializeMcpServers()
    }

    getServers(): McpServer[] {
		// Only return enabled servers

		return this.connections.filter((conn) => !conn.server.disabled).map((conn) => conn.server)
	}


    private async watchMcpSettingsFile(): Promise<void> {
        const settingsPath = await this.getMcpSettingsFilePath()

        // TODO 监听 MCP 设置文件的变化并自动更新服务器连接 。
    }

    async getMcpSettingsFilePath(): Promise<string> {
        const mcpSettingsFilePath = await this.getSettingsFilePath()
        const fileExists = await fileExistsAtPath(mcpSettingsFilePath)
        if (!fileExists) {
            await fs.writeFile(
                mcpSettingsFilePath,
                `{
  "mcpServers": {
    
  }
}`,
            )
        }
        return mcpSettingsFilePath
    }

    private async initializeMcpServers(): Promise<void> {
        const settings = await this.readAndValidateMcpSettingsFile()
        if (settings) {
            await this.updateServerConnections(settings.mcpServers)
        }
    }

    private async readAndValidateMcpSettingsFile(): Promise<
        z.infer<typeof McpSettingsSchema> | undefined
    > {
        try {
            const settingsPath = await this.getMcpSettingsFilePath()
            const content = await fs.readFile(settingsPath, 'utf-8')

            let config: any

            // Parse JSON file content
            try {
                config = JSON.parse(content)
            } catch (error) {
                vscode.window.showErrorMessage(
                    'Invalid MCP settings format. Please ensure your settings follow the correct JSON format.',
                )
                return undefined
            }

            // Validate against schema
            const result = McpSettingsSchema.safeParse(config)
            if (!result.success) {
                vscode.window.showErrorMessage('Invalid MCP settings schema.')
                return undefined
            }

            return result.data
        } catch (error) {
            console.error('Failed to read MCP settings:', error)
            return undefined
        }
    }

    async updateServerConnections(newServers: Record<string, McpServerConfig>): Promise<void> {
        this.isConnecting = true
        const currentNames = new Set(this.connections.map(conn => conn.server.name))
        const newNames = new Set(Object.keys(newServers))

        // Delete removed servers
        for (const name of currentNames) {
            if (!newNames.has(name)) {
                await this.deleteConnection(name)
                console.log(`Deleted MCP server: ${name}`)
            }
        }

        // Update or add servers
        for (const [name, config] of Object.entries(newServers)) {
            const currentConnection = this.connections.find(conn => conn.server.name === name)

            if (!currentConnection) {
                // New server
                try {
                    if (config.type === 'stdio') {
                        this.setupFileWatcher(name, config)
                    }
                    await this.connectToServer(name, config, 'internal')
                } catch (error) {
                    console.error(`Failed to connect to new MCP server ${name}:`, error)
                }
            } else if (!deepEqual(JSON.parse(currentConnection.server.config), config)) {
                // Existing server with changed config
                try {
                    if (config.type === 'stdio') {
                        this.setupFileWatcher(name, config)
                    }
                    await this.deleteConnection(name)
                    await this.connectToServer(name, config, 'internal')
                    console.log(`Reconnected MCP server with updated config: ${name}`)
                } catch (error) {
                    console.error(`Failed to reconnect MCP server ${name}:`, error)
                }
            }
            // If server exists with same config, do nothing
        }
        await this.notifyWebviewOfServerChanges()
        this.isConnecting = false
    }

    async deleteConnection(name: string): Promise<void> {
        const connection = this.connections.find(conn => conn.server.name === name)
        if (connection) {
            try {
                await connection.transport.close()
                await connection.client.close()
            } catch (error) {
                console.error(`Failed to close transport for ${name}:`, error)
            }
            this.connections = this.connections.filter(conn => conn.server.name !== name)
        }
    }

    private setupFileWatcher(name: string, config: Extract<McpServerConfig, { type: 'stdio' }>) {
        const filePath = config.args?.find((arg: string) => arg.includes('build/index.js'))
        if (filePath) {
            // we use chokidar instead of onDidSaveTextDocument because it doesn't require the file to be open in the editor. The settings config is better suited for onDidSave since that will be manually updated by the user or Cline (and we want to detect save events, not every file change)
            const watcher = chokidar.watch(filePath, {
                // persistent: true,
                // ignoreInitial: true,
                // awaitWriteFinish: true, // This helps with atomic writes
            })

            watcher.on('change', () => {
                console.log(`Detected change in ${filePath}. Restarting server ${name}...`)
                this.restartConnection(name)
            })

            this.fileWatchers.set(name, watcher)
        }
    }

    async restartConnection(serverName: string): Promise<void> {
        this.isConnecting = true

        // Get existing connection and update its status
        const connection = this.connections.find(conn => conn.server.name === serverName)
        const config = connection?.server.config
        if (config) {
            vscode.window.showInformationMessage(`Restarting ${serverName} MCP server...`)
            connection.server.status = 'connecting'
            connection.server.error = ''
            await this.notifyWebviewOfServerChanges()
            await setTimeoutPromise(500) // artificial delay to show user that server is restarting
            try {
                await this.deleteConnection(serverName)
                // Try to connect again using existing config
                await this.connectToServer(serverName, JSON.parse(config), 'internal')
                vscode.window.showInformationMessage(`${serverName} MCP server connected`)
            } catch (error) {
                console.error(`Failed to restart connection for ${serverName}:`, error)
                vscode.window.showErrorMessage(`Failed to connect to ${serverName} MCP server`)
            }
        }

        await this.notifyWebviewOfServerChanges()
        this.isConnecting = false
    }

    private async notifyWebviewOfServerChanges(): Promise<void> {
        // servers should always be sorted in the order they are defined in the settings file
        const settingsPath = await this.getMcpSettingsFilePath()
        const content = await fs.readFile(settingsPath, 'utf-8')
        const config = JSON.parse(content)
        const serverOrder = Object.keys(config.mcpServers || {})

        const sortedServers = this.getSortedMcpServers(serverOrder)

        // TODO LJW Send update using gRPC stream
    }

    /**
     * Gets sorted MCP servers based on the order defined in settings
     * @param serverOrder Array of server names in the order they appear in settings
     * @returns Array of McpServer objects sorted according to settings order
     */
    private getSortedMcpServers(serverOrder: string[]): McpServer[] {
        return [...this.connections]
            .sort((a, b) => {
                const indexA = serverOrder.indexOf(a.server.name)
                const indexB = serverOrder.indexOf(b.server.name)
                return indexA - indexB
            })
            .map(connection => connection.server)
    }

    private async connectToServer(
        name: string,
        config: z.infer<typeof ServerConfigSchema>,
        source: 'rpc' | 'internal',
    ): Promise<void> {
        // Remove existing connection if it exists (should never happen, the connection should be deleted beforehand)
        this.connections = this.connections.filter(conn => conn.server.name !== name)

        try {
            // Each MCP server requires its own transport connection and has unique capabilities, configurations, and error handling. Having separate clients also allows proper scoping of resources/tools and independent server management like reconnection.
            const client = new Client(
                {
                    name: 'Cline',
                    version: this.clientVersion,
                },
                {
                    capabilities: {},
                },
            )

            let transport: StdioClientTransport | SSEClientTransport | StreamableHTTPClientTransport

            switch (config.type) {
                case 'stdio': {
                    transport = new StdioClientTransport({
                        command: config.command,
                        args: config.args,
                        cwd: config.cwd,
                        env: {
                            // ...(config.env ? await injectEnv(config.env) : {}), // Commented out as injectEnv is not found
                            ...(config.env || {}), // Use config.env directly or an empty object
                            ...(process.env.PATH ? { PATH: process.env.PATH } : {}),
                        },
                        stderr: 'pipe',
                    })

                    transport.onerror = async error => {
                        console.error(`Transport error for "${name}":`, error)
                        const connection = this.findConnection(name, source)
                        if (connection) {
                            connection.server.status = 'disconnected'
                            this.appendErrorMessage(
                                connection,
                                error instanceof Error ? error.message : `${error}`,
                            )
                        }
                        await this.notifyWebviewOfServerChanges()
                    }

                    transport.onclose = async () => {
                        const connection = this.findConnection(name, source)
                        if (connection) {
                            connection.server.status = 'disconnected'
                        }
                        await this.notifyWebviewOfServerChanges()
                    }

                    await transport.start()
                    const stderrStream = transport.stderr
                    if (stderrStream) {
                        stderrStream.on('data', async (data: Buffer) => {
                            const output = data.toString()
                            const isInfoLog = /INFO/i.test(output)

                            if (isInfoLog) {
                                console.log(`Server "${name}" info:`, output)
                            } else {
                                console.error(`Server "${name}" stderr:`, output)
                                const connection = this.findConnection(name, source)
                                if (connection) {
                                    this.appendErrorMessage(connection, output)
                                    if (connection.server.status === 'disconnected') {
                                        await this.notifyWebviewOfServerChanges()
                                    }
                                }
                            }
                        })
                    } else {
                        console.error(`No stderr stream for ${name}`)
                    }
                    transport.start = async () => {}
                    break
                }
                case 'sse': {
                    const sseOptions = {
                        requestInit: {
                            headers: config.headers,
                        },
                    }
                    const reconnectingEventSourceOptions = {
                        max_retry_time: 5000,
                        withCredentials: config.headers?.['Authorization'] ? true : false,
                    }

                    // TODO LJW
                    // @ts-ignore
                    global.EventSource = ReconnectingEventSource
                    transport = new SSEClientTransport(new URL(config.url), {
                        ...sseOptions,
                        eventSourceInit: reconnectingEventSourceOptions,
                    })

                    transport.onerror = async error => {
                        console.error(`Transport error for "${name}":`, error)
                        const connection = this.findConnection(name, source)
                        if (connection) {
                            connection.server.status = 'disconnected'
                            this.appendErrorMessage(
                                connection,
                                error instanceof Error ? error.message : `${error}`,
                            )
                        }
                        await this.notifyWebviewOfServerChanges()
                    }
                    break
                }
                case 'streamableHttp': {
                    transport = new StreamableHTTPClientTransport(new URL(config.url), {
                        requestInit: {
                            headers: config.headers,
                        },
                    })
                    transport.onerror = async error => {
                        console.error(`Transport error for "${name}":`, error)
                        const connection = this.findConnection(name, source)
                        if (connection) {
                            connection.server.status = 'disconnected'
                            this.appendErrorMessage(
                                connection,
                                error instanceof Error ? error.message : `${error}`,
                            )
                        }
                        await this.notifyWebviewOfServerChanges()
                    }
                    break
                }
                default:
                    throw new Error(`Unknown transport type: ${(config as any).type}`)
            }

            const connection: McpConnection = {
                server: {
                    name,
                    config: JSON.stringify(config),
                    status: 'connecting',
                    disabled: config.disabled,
                },
                client,
                transport,
            }
            this.connections.push(connection)

            // Connect
            await client.connect(transport)

            connection.server.status = 'connected'
            connection.server.error = ''

            // Initial fetch of tools and resources
            connection.server.tools = await this.fetchToolsList(name)
            connection.server.resources = await this.fetchResourcesList(name)
            connection.server.resourceTemplates = await this.fetchResourceTemplatesList(name)
        } catch (error) {
            // Update status with error
            const connection = this.findConnection(name, source)
            if (connection) {
                connection.server.status = 'disconnected'
                this.appendErrorMessage(
                    connection,
                    error instanceof Error ? error.message : String(error),
                )
            }
            throw error
        }
    }

    private findConnection(name: string, source: 'rpc' | 'internal'): McpConnection | undefined {
        return this.connections.find(conn => conn.server.name === name)
    }

    private async fetchToolsList(serverName: string): Promise<McpTool[]> {
        try {
            const connection = this.connections.find(conn => conn.server.name === serverName)

            if (!connection) {
                throw new Error(`No connection found for server: ${serverName}`)
            }

            const response = await connection.client.request(
                { method: 'tools/list' },
                ListToolsResultSchema,
                {
                    timeout: DEFAULT_REQUEST_TIMEOUT_MS,
                },
            )

            // Get autoApprove settings
            const settingsPath = await this.getMcpSettingsFilePath()
            const content = await fs.readFile(settingsPath, 'utf-8')
            const config = JSON.parse(content)
            const autoApproveConfig = config.mcpServers[serverName]?.autoApprove || []

            // Mark tools as always allowed based on settings
            const tools = (response?.tools || []).map(tool => ({
                ...tool,
                autoApprove: autoApproveConfig.includes(tool.name),
            }))

            return tools
        } catch (error) {
            console.error(`Failed to fetch tools for ${serverName}:`, error)
            return []
        }
    }

    private appendErrorMessage(connection: McpConnection, error: string) {
        const newError = connection.server.error ? `${connection.server.error}\n${error}` : error
        connection.server.error = newError //.slice(0, 800)
    }

    private async fetchResourcesList(serverName: string): Promise<McpResource[]> {
        try {
            const response = await this.connections
                .find(conn => conn.server.name === serverName)
                ?.client.request({ method: 'resources/list' }, ListResourcesResultSchema, {
                    timeout: DEFAULT_REQUEST_TIMEOUT_MS,
                })
            return response?.resources || []
        } catch (error) {
            // console.error(`Failed to fetch resources for ${serverName}:`, error)
            return []
        }
    }

    private async fetchResourceTemplatesList(serverName: string): Promise<McpResourceTemplate[]> {
        try {
            const response = await this.connections
                .find(conn => conn.server.name === serverName)
                ?.client.request(
                    { method: 'resources/templates/list' },
                    ListResourceTemplatesResultSchema,
                    {
                        timeout: DEFAULT_REQUEST_TIMEOUT_MS,
                    },
                )

            return response?.resourceTemplates || []
        } catch (error) {
            // console.error(`Failed to fetch resource templates for ${serverName}:`, error)
            return []
        }
    }

    async callTool(
        serverName: string,
        toolName: string,
        toolArguments?: Record<string, unknown>,
    ): Promise<McpToolCallResponse> {
        const connection = this.connections.find(conn => conn.server.name === serverName)
        if (!connection) {
            throw new Error(
                `No connection found for server: ${serverName}. Please make sure to use MCP servers available under 'Connected MCP Servers'.`,
            )
        }

        if (connection.server.disabled) {
            throw new Error(`Server "${serverName}" is disabled and cannot be used`)
        }

        let timeout = DEFAULT_MCP_TIMEOUT_SECONDS * 1000 // sdk expects ms

        try {
            const config = JSON.parse(connection.server.config)
            const parsedConfig = ServerConfigSchema.parse(config)
            timeout = parsedConfig.timeout * 1000
        } catch (error) {
            console.error(
                `Failed to parse timeout configuration for server ${serverName}: ${error}`,
            )
        }

        const result = await connection.client.request(
            {
                method: 'tools/call',
                params: {
                    name: toolName,
                    arguments: toolArguments,
                },
            },
            CallToolResultSchema,
            {
                timeout,
            },
        )

        return {
            ...result,
            content: result.content ?? [],
        }
    }

    async readResource(serverName: string, uri: string): Promise<McpResourceResponse> {
        const connection = this.connections.find(conn => conn.server.name === serverName)
        if (!connection) {
            throw new Error(`No connection found for server: ${serverName}`)
        }
        if (connection.server.disabled) {
            throw new Error(`Server "${serverName}" is disabled`)
        }

        return await connection.client.request(
            {
                method: 'resources/read',
                params: {
                    uri,
                },
            },
            ReadResourceResultSchema,
        )
    }

    async dispose(): Promise<void> {
        this.removeAllFileWatchers()
        for (const connection of this.connections) {
            try {
                await this.deleteConnection(connection.server.name)
            } catch (error) {
                console.error(`Failed to close connection for ${connection.server.name}:`, error)
            }
        }
        this.connections = []
        if (this.settingsWatcher) {
            this.settingsWatcher.dispose()
        }
        this.disposables.forEach(d => d.dispose())
    }

    private removeAllFileWatchers() {
        this.fileWatchers.forEach(watcher => watcher.close())
        this.fileWatchers.clear()
    }
}
