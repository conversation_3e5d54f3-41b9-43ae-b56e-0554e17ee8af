import React from 'react';
import { IconButton } from '@radix-ui/themes';
import { GearIcon, TrashIcon, PlusIcon } from '@radix-ui/react-icons';

interface ChatHeaderProps {
  onClearChat: () => void;
  onNewChat: () => void;
}

export const ChatHeader: React.FC<ChatHeaderProps> = ({ onClearChat, onNewChat }) => {
  return (
    <div className="flex items-center justify-between py-3 px-0 border-b vscode-border vscode-bg-primary">
      <h1 className="text-lg font-medium vscode-text-primary">AI编程助手</h1>
      <div className="flex gap-2">
        <IconButton
          size="1"
          variant="ghost"
          onClick={onNewChat}
          title="新建聊天"
          className="vscode-text-primary hover:vscode-hover"
        >
          <PlusIcon />
        </IconButton>
        <IconButton
          size="1"
          variant="ghost"
          onClick={onClearChat}
          title="清空对话"
          className="vscode-text-primary hover:vscode-hover"
        >
          <TrashIcon />
        </IconButton>
        <IconButton
          size="1"
          variant="ghost"
          title="设置"
          className="vscode-text-primary hover:vscode-hover"
        >
          <GearIcon />
        </IconButton>
      </div>
    </div>
  );
};