import { Node } from 'pocketflow'
import { listDir } from '../utils/listDir'
import { CodeAgentActionEnum, CodingAgentSharedStore, History, Output } from '../types'
import path from 'path'

interface PrepResult {
    dirPath: string
    history: History[]
    output: Output
}

export class ListDirActionNode extends Node<CodingAgentSharedStore> {
    async prep(shared: CodingAgentSharedStore): Promise<PrepResult> {
        const relativePath = shared.currentAction!.params.relativeWorkspacePath
        const dirPath = path.resolve(shared.workingDir, relativePath)

        await shared.output.text(`ListDirAction - prep: 目录路径: ${dirPath}`)

        return { dirPath, history: shared.history, output: shared.output }
    }

    async exec(prepResult: PrepResult): Promise<{ treeStr: string; success: boolean }> {
        await prepResult.output.text(`正在获取目录信息: ${prepResult.dirPath}`)
        const [success, treeStr] = await listDir(prepResult.dirPath)

        return { success, treeStr }
    }

    async post(
        shared: CodingAgentSharedStore,
        prepResult: PrepResult,
        execResult: { treeStr: string; success: boolean },
    ) {
        shared.history.push({
            role: 'user',
            content: JSON.stringify({
                ...shared.currentAction!,
                success: execResult.success,
                treeVisualization: execResult.treeStr,
            }),
        })

        shared.currentAction = null

        return CodeAgentActionEnum.default
    }
}
