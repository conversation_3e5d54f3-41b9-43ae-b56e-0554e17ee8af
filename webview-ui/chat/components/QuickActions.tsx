import React from 'react';
import { Button } from '@radix-ui/themes';

interface QuickActionsProps {
  onInsertText: (text: string) => void;
}

const quickActions = [
  {
    label: '代码优化',
    text: '请帮我优化这段代码：\n```\n\n```'
  },
  {
    label: '代码解释',
    text: '请帮我解释这段代码的作用：\n```\n\n```'
  },
  {
    label: 'Bug修复',
    text: '请帮我找出这段代码的bug：\n```\n\n```'
  },
  {
    label: '代码生成',
    text: '请帮我写一个'
  }
];

export const QuickActions: React.FC<QuickActionsProps> = ({ onInsertText }) => {
  return (
    <div className="flex gap-2 flex-wrap">
      {quickActions.map((action, index) => (
        <Button
          key={index}
          size="1"
          variant="soft"
          onClick={() => onInsertText(action.text)}
          className="vscode-text-primary vscode-bg-secondary hover:vscode-hover vscode-border"
          style={{
            backgroundColor: 'var(--vscode-badge-background)',
            color: 'var(--vscode-badge-foreground)',
            borderColor: 'var(--vscode-input-border)'
          }}
        >
          {action.label}
        </Button>
      ))}
    </div>
  );
};