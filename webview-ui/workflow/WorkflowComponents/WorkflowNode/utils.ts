import { nodeInfoMap } from "./config";
import {
  WorkflowConditionItem,
  WorkflowConditionOperatorEnum,
  WorkflowData,
  WorkflowIOValueTypeEnum,
  ReactFlowNodeConfig,
  WorkflowNodeTypeEnum,
} from "@lvjw/code-workflow-types";

import { v4 as uuidv4 } from "uuid";

// 添加节点的方法，在 inputOrOutput 模板的基础上追加 id 字段，修改 label
export const createNewNode = (
  nodeType: WorkflowNodeTypeEnum,
  existingNodes: ReactFlowNodeConfig[]
): ReactFlowNodeConfig => {
  let nodeConfigData = nodeInfoMap.getData(nodeType);

  if (!nodeConfigData) {
    throw new Error(`节点类型 ${nodeType} 不存在`);
  }

  nodeConfigData = formatNodeIOData(nodeConfigData);

  nodeConfigData.label = getNodeLabel(nodeType, existingNodes);

  return {
    id: uuidv4().replaceAll("-", ""),
    type: nodeType,
    position: { x: 0, y: 0 },
    data: {
      ...nodeConfigData,
      showInputs: nodeConfigData.showInputs ?? true,
      showOutputs: nodeConfigData.showOutputs ?? true,
      hasCustomHandle: nodeConfigData.hasCustomHandle ?? false,
    },
  };
};

const formatNodeIOData = (nodeConfigData: WorkflowData) => {
  const clone = JSON.parse(JSON.stringify(nodeConfigData));
  if (clone?.inputs?.length) {
    clone.inputs = clone.inputs.map((input) => ({
      ...input,
      id: uuidv4().replaceAll("-", ""),
    }));
  }
  if (clone?.outputs?.length) {
    clone.outputs = clone.outputs.map((output) => ({
      ...output,
      id: uuidv4().replaceAll("-", ""),
    }));
  }
  return clone;
};
const getNodeLabel = (
  nodeType: WorkflowNodeTypeEnum,
  existingNodes: ReactFlowNodeConfig[]
) => {
  const existingSameTypeNode = (existingNodes ?? []).filter(
    (node) => node.type === nodeType
  );
  let label = nodeInfoMap.getLabel(nodeType);
  return `${label}${
    existingSameTypeNode.length ? `${existingSameTypeNode.length + 1}` : ""
  }`;
};

export const createConditionItem = (): WorkflowConditionItem => {
  return {
    left: {
      id: uuidv4().replaceAll("-", ""),
      attrName: "left",
      valueType: WorkflowIOValueTypeEnum.any,
      isDynamic: true,
      value: "",
      valuePath: [],
      valueLabelPath: [],
      label: "left",
      removable: true,
      editable: true,
    },
    operator: WorkflowConditionOperatorEnum.equal,
    right: {
      id: uuidv4().replaceAll("-", ""),
      attrName: "right",
      valueType: WorkflowIOValueTypeEnum.string,
      isDynamic: true,
      label: "right",
      value: "",
      removable: true,
      editable: true,
    },
  };
};
