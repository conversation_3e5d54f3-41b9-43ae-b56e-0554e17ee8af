import { Node } from 'pocketflow'
import { callLlm } from '../utils/callLlm'
import { CodingAgentSharedStore, EditOperation, EditCodeAgentActionEnum, History, Output } from '../types'
import { extractJsonBlockFromMarkdownText } from '../utils/common'
import { genCodeEditPrompt } from '../utils/genCodeEditPrompt'
import { McpHub } from '../../../services/mcp/McpHub'


interface PrepResult {
  fileContent: string
    instructions: string
    codeEdit: string
    mcpHub?: McpHub
    history: History[]
    output: Output
}

export class AnalyzeAndPlanChangesNode extends Node<CodingAgentSharedStore> {
  async prep(shared: CodingAgentSharedStore): Promise<PrepResult> {
    shared.output.text('正在分析和计划变更...')
    
    return {
      fileContent: shared.currentAction!.result,
      instructions: shared.currentAction!.params.instructions,
      codeEdit: shared.currentAction!.params.codeEdit,
      mcpHub: shared.mcpHub,
      history: shared.history,
      output: shared.output,
    }
  }

  async exec(prepResult: PrepResult): Promise<{ edits: EditOperation[] }> {
    
    const prompt = genCodeEditPrompt(prepResult.fileContent, prepResult.instructions, prepResult.codeEdit)

    const response = await callLlm([{
      role: 'user',
      content: prompt
    }])
    prepResult.output.text(`分析编辑计划结果，${response}`)
    const responseJson = extractJsonBlockFromMarkdownText(response) as any

    
    if (!responseJson) {
      throw new Error('Failed to parse edit plan')
    } else {
      return responseJson.operations
    }
  }

  async post(
    shared: CodingAgentSharedStore,
    prepResult: PrepResult,
    execRes:  EditOperation[]
  ): Promise<string> {
    
    shared.editOperations = execRes

    // TODO
    
    return  EditCodeAgentActionEnum.default
  }
}