import { Node } from 'pocketflow'
import { readFile } from '../utils/readFile'
import { CodeAgentActionEnum, CodingAgentSharedStore, History, NodeResult, Output } from '../types'
import path from 'path'

interface PrepResult {
    pathParameter: string
    filePath: string
    startLineOneIndexed?: number
    endLineOneIndexed?: number
    output: Output
}

export class ReadFileActionNode extends Node<CodingAgentSharedStore> {
    async prep(shared: CodingAgentSharedStore): Promise<PrepResult> {
        const lastAction = shared.currentAction!
        const filePath = path.resolve(shared.workingDir, lastAction.params.path)
        const startLineOneIndexed = lastAction.params.startLineOneIndexed
        const endLineOneIndexed = lastAction.params.endLineOneIndexedInclusive

        return {
            pathParameter: lastAction.params.path,
            filePath,
            startLineOneIndexed,
            endLineOneIndexed,
            output: shared.output,
        }
    }

    async exec(prepResult: PrepResult): Promise<{ content: string; success: boolean }> {
        await prepResult.output.text(`ReadFileAction - exec: 读取文件 ${prepResult.filePath}`)

        prepResult.output.text(`读取文件 ${prepResult.pathParameter}`)
        // TODO 最多读 50 行
        const [success, content] = await readFile(
            prepResult.filePath,
            prepResult.startLineOneIndexed,
            prepResult.endLineOneIndexed,
        )

        await prepResult.output.text(`读取文件 ${prepResult.filePath} ${success ? '成功' : '失败'}`)

        return { success, content }
    }

    async post(
        shared: CodingAgentSharedStore,
        prepResult: PrepResult,
        execResult: { content: string; success: boolean },
    ) {
        shared.history.push({
            role: 'user',
            content: JSON.stringify({
                ...shared.currentAction,
                result: execResult.content,
                success: execResult.success,
            }),
        })

        shared.currentAction = null

        return CodeAgentActionEnum.default
    }
}
