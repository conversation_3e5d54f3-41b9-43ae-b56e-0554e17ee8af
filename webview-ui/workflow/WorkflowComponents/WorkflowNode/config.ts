
import {  SimpleWorkflowNode } from "./SimpleWorkflowNode";
import { ConditionWorkflowNode } from "./ConditionWorkflowNode";
import { WorkflowNodeTypeEnum , WorkflowData, WorkflowIOValueTypeEnum} from "@lvjw/code-workflow-types";

// 1. 定义 nodeInfoData
const nodeInfoData = {
  [WorkflowNodeTypeEnum.systemConfig]: {
    renderer: SimpleWorkflowNode,
    data: {
      inputs: [],
      outputs: [],
    },
    label: "系统配置",
  },
  [WorkflowNodeTypeEnum.globalVariable]: {
    renderer: SimpleWorkflowNode,
    data: {
      inputs: [],
      outputs: [],
    },
    label: "全局变量",
  },
  [WorkflowNodeTypeEnum.workflowStart]: {
    renderer: SimpleWorkflowNode,
    data: {
      showInputs: true,
      showOutputs: false,
      inputs: [],
      outputs: [],
    },
    showInputs: true,
    showOutputs: false,
    label: "工作流开始",
  },
  [WorkflowNodeTypeEnum.workflowEnd]: {
    renderer: SimpleWorkflowNode,
    data: {
      showInputs: false,
      showOutputs: true,
      outputs: [
        {
          attrName: "result",
          label: "结果",
          removable: true,
          editable: true,
          valueType: WorkflowIOValueTypeEnum.string,
          isDynamic: true,
          valuePath: [],
          valueLabelPath: [], // 新增: 存储选定的标签路径
          value: "",
        },
      ],
    },
    label: "工作流结束",
  },
  [WorkflowNodeTypeEnum.condition]: {
    renderer: ConditionWorkflowNode,
    data: {
      hasCustomHandle: true,
      conditions: []
    },
    label: "条件",
  },
  [WorkflowNodeTypeEnum.code]: {
    renderer: SimpleWorkflowNode,
    data: {
      showInputs: true,
      showOutputs: true,
      inputs: [
        {
          attrName: "input1",
          label: "",
          removable: true,
          editable: true,
          valueType: WorkflowIOValueTypeEnum.string,
          isDynamic: false,
          valuePath: [],
          valueLabelPath: [], // 新增: 存储选定的标签路径
          value: "",
        },
        {
          attrName: "code",
          label: "代码",
          removable: true,
          editable: true,
          valueType: WorkflowIOValueTypeEnum.code,
          isDynamic: false,
          valuePath: [],
          valueLabelPath: [], // 新增: 存储选定的标签路径
          value: "",
        },
      ],
      outputs: [
        {
          attrName: "return",
          label: "函数返回",
          removable: false,
          editable: false,
          valueType: WorkflowIOValueTypeEnum.any,
          isDynamic: false,
          valuePath: [],
          valueLabelPath: [], // 新增: 存储选定的标签路径
          value: "",
        },
      ],
    },
    label: "代码",
  },

  [WorkflowNodeTypeEnum.loop]: {
    renderer: SimpleWorkflowNode,
    data: {
      inputs: [],
      outputs: [],
    },
    label: "循环",
  },
  [WorkflowNodeTypeEnum.loopStart]: {
    renderer: SimpleWorkflowNode,
    data: {
      inputs: [],
      outputs: [],
    },
    label: "循环开始",
  },
  [WorkflowNodeTypeEnum.loopEnd]: {
    renderer: SimpleWorkflowNode,
    data: {
      inputs: [],
      outputs: [],
    },
    label: "循环结束",
  },
  [WorkflowNodeTypeEnum.chatWithLLM]: {
    renderer: SimpleWorkflowNode,
    data: {
      showInputs: true,
      showOutputs: true,
      inputs: [
        {
          attrName: "modelId",
          label: "模型",
          removable: false,
          editable: false,
          valueType: WorkflowIOValueTypeEnum.modelId,
        },
        {
          attrName: "apiKey",
          label: "API Key",
          removable: false,
          editable: false,
          valueType: WorkflowIOValueTypeEnum.password,
        },
        {
          attrName: "systemPrompt",
          label: "系统提示",
          removable: false,
          editable: false,
          valueType: WorkflowIOValueTypeEnum.longText,
        },
        {
          attrName: "userInput",
          label: "用户输入",
          removable: false,
          isDynamic: true,
          editable: false,
          valueType: WorkflowIOValueTypeEnum.longText,
        },
      ],
      outputs: [
        {
          attrName: "modelOutput",
          label: "模型回复内容",
          removable: false,
          editable: false,
          valueType: WorkflowIOValueTypeEnum.string,
        },
      ],
    },
    label: "与 LLM 聊天",
  },
  [WorkflowNodeTypeEnum.dbQuery]: {
    renderer: SimpleWorkflowNode,
    data: {
      showInputs: true,
      showOutputs: true,
      inputs: [
        {
          attrName: "query",
          label: "查询语句",
          removable: false,
          editable: false,
          valueType: WorkflowIOValueTypeEnum.stringWithLLM,
        },
      ],
      outputs: [
        {
          attrName: "result",
          label: "结果",
          removable: true,
          editable: true,
          valueType: WorkflowIOValueTypeEnum.string,
          isDynamic: true,
          valuePath: [],
        }
      ]
    },
    label: "数据库查询",
  },
  [WorkflowNodeTypeEnum.httpRequest]: {
    renderer: SimpleWorkflowNode,
    data: {
      showInputs: true,
      showOutputs: true,
      inputs: [
        {
          attrName: "config",
          label: "请求配置",
          removable: false,
          editable: false,
          valueType: WorkflowIOValueTypeEnum.httpRequestOptions,
          value: {
            method: 'POST',
            url: '',
            timeout: 30,
            params: [{ type: 'const', key: '', value: '', valuePath: [], valueLabelPath: [] }],
            headers: [{ type: 'const', key: '', value: '', valuePath: [], valueLabelPath: [] }],
            bodyType: 'json',
            body: '',
          }
        },
      ],
      outputs: [
        {
          attrName: "result",
          label: "结果",
          removable: true,
          editable: true,
          valueType: WorkflowIOValueTypeEnum.string,
          isDynamic: true,
          valuePath: [],
        }
      ]
    },
    label: "HTTP 请求",
  },
};

// 2. 工厂函数
function createNodeInfoMap(data: typeof nodeInfoData) {
  return {
    ...data,
    getNodeTypes() {
      return Object.keys(data).reduce((acc, key) => {
        acc[key] = data[key].renderer;
        return acc;
      }, {} as any);
    },
    getData(nodeType: WorkflowNodeTypeEnum): WorkflowData {
      return data[nodeType]?.data as WorkflowData;
    },
    getLabel(nodeType: WorkflowNodeTypeEnum) {
      return data[nodeType]?.label;
    },
    getRenderer(nodeType: WorkflowNodeTypeEnum) {
      return data[nodeType]?.renderer;
    },
    getNodeOptions() {
      return Object.keys(data).map((key) => ({
        value: key,
        label: data[key].label,
      }));
    },
  };
}

// 3. 创建最终 nodeInfoMap
export const nodeInfoMap = createNodeInfoMap(nodeInfoData);

export default nodeInfoMap;
