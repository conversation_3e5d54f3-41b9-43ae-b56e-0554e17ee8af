import React from "react";
import { WorkflowNodeInputFieldRenderProps } from "./interface";
import { useNodeInputsState } from "../WorkflowCanvas";
import CodeEditor from '../../LibComponents/CodeEditor'
import { TextField } from "@radix-ui/themes";
import { ModelIdRenderer } from "./ModelIdRenderer";
import { LongTextRenderer } from "./LongTextRenderer";
import { PasswordRenderer } from "./PasswordRenderer";
import { PrevNodeFieldSelector } from '../common/PrevNodeFieldSelector/PrevNodeFieldSelector';
import { WorkflowIOValueTypeEnum } from "@lvjw/code-workflow-types";
import { StringWithLLMRenderer } from "./StringWithLLMRenderer";
import HTTPRequestConfigRenderer from "./HTTPRequestConfigRenderer";

const valueType2RendererMap: Record<any, any> = {
  [WorkflowIOValueTypeEnum.code]: CodeE<PERSON>or,
  [WorkflowIOValueTypeEnum.modelId]: ModelId<PERSON><PERSON><PERSON>,
  [WorkflowIOValueTypeEnum.longText]: LongTextRenderer,
  [WorkflowIOValueTypeEnum.password]: PasswordRenderer,
  [WorkflowIOValueTypeEnum.stringWithLLM]: StringWithLLMRenderer,
  [WorkflowIOValueTypeEnum.httpRequestOptions]: HTTPRequestConfigRenderer,
};

export const WorkflowNodeInputFieldRenderer: React.FC<WorkflowNodeInputFieldRenderProps> = ({ 
  input, 
  node 
}) => {
  const {updateInput}  = useNodeInputsState(node.id)

  const handleCascaderChange = (selected: { path: string[]; label: string , labelPath: string[]}) => {
    updateInput(input.id, {
      ...input,
      valuePath: selected.path,
      valueLabelPath: selected.labelPath
    })
  };

  const handleValueChange = (eventOrValue: React.ChangeEvent<HTMLInputElement> | any) => {
    updateInput(input.id, {
      ...input,
      value: eventOrValue.target?.value ?? eventOrValue
    })
  }

  if (input.isDynamic) {
    return (
      <PrevNodeFieldSelector
        currentNodeId={node.id}
        valuePath={input.valuePath}
        valueLabelPath={input.valueLabelPath}
        onChange={handleCascaderChange}
      />
    );
  } else {
    const Comp = valueType2RendererMap[input.valueType]
    if (Comp) {
      return <Comp nodeId={node.id} value={input.value} onChange={handleValueChange}></Comp>
    } else {
      return <TextField.Root placeholder="请输入..." value={input.value} onChange={handleValueChange}></TextField.Root>
    }
  } 
};
