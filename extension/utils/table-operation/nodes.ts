// generateTableNode
import * as vscode from "vscode";
import { Node } from "pocketflow";
import { generateText, streamText } from "ai"; // Import streamText
import { deepseek, parseMessageToJson } from "../llm";
import { getGenerateTableCodePrompt, getRequirementAnalysisPrompt } from "./prompt";
import { Result } from "pg";
import { parseEntityToSchema } from './entity-parser';
import { getTableAndColumnInfo } from "./utils";

export type SharedStorage = {
  userRequirement: string;
  clarificationQuestions: string[];
  clarification: string;
  dbTableDoc: string;
  panel: vscode.WebviewPanel;
  sessionId: string;
  workspaceFolder: vscode.WorkspaceFolder;
};

export enum Actions {
  AnalyzeRequirement = "AnalyzeRequirement",
  NeedClarification = "NeedClarification",
  WriteTableDoc = "WriteTableDoc",
}

type AnalysisResult = {
  needsClarification: boolean;
  questions: string[];
  requirement: string;
};

export class RequirementAnalysisNode extends Node {
  constructor(maxRetries?: number) {
    super(maxRetries);
  }

  override async prep(
    shared: SharedStorage
  ): Promise<Pick<SharedStorage, "userRequirement" | "clarification" | "panel" | "sessionId">> { // Add panel and sessionId
    if (!shared.userRequirement) {
      throw new Error("User requirement is not set");
    }
    console.log('====> shared', shared )
    return {
      userRequirement: shared.userRequirement,
      clarification: shared.clarification,
      panel: shared.panel, // Pass panel
      sessionId: shared.sessionId, // Pass sessionId
    };
  }

  override async exec({
    userRequirement,
    clarification,
    panel, // Destructure panel
    sessionId, // Destructure sessionId
  }: Pick<
    SharedStorage,
    "userRequirement" | "clarification" | "panel" | "sessionId"
  >): Promise<AnalysisResult> {
    console.log(`Analyzing requirement...`);
    const result = streamText({
      model: deepseek("deepseek-chat"),
      prompt: getRequirementAnalysisPrompt(userRequirement, clarification),
    });

    let fullText = "";
    // Stream text to webview
    for await (const textPart of result.textStream) {
      fullText += textPart;
      panel.webview.postMessage({
        type: 'llmChunk',
        sessionId: sessionId,
        content: textPart,
      });
    }
    
    console.log(`Analysis stream finished. Full result: ${fullText}`);

    // After streaming is complete, send a final message indicating completion (optional)
    panel.webview.postMessage({
      type: 'requirementAnalysisStreamEnd',
      sessionId: sessionId,
    });

    return parseMessageToJson(fullText);
  }

  override async post(
    shared: SharedStorage,
    _prepRes: unknown,
    execRes: AnalysisResult
  ): Promise<string | undefined> {
    console.log("Post-processing analysis result...", execRes);

    // Send final analysis result to webview (this might be redundant if UI is fully updated by stream)
    // Consider if this is still needed or if the UI can derive the final state from the stream.
    shared.panel.webview.postMessage({
      type: 'requirementAnalysisResult',
      sessionId: shared.sessionId,
      payload: execRes 
    });

    if (execRes.needsClarification) {
      console.log("Requirement needs clarification.");
      shared.clarificationQuestions = execRes.questions;
      return Actions.NeedClarification;
    } else {
      console.log("Requirement is clear.");
      // TODO: 好的需求已明确，[具体需求],正在为您生成模型...
      shared.userRequirement = execRes.requirement;
      // Add this line to send a message to the webview when the requirement is clear
      shared.panel.webview.postMessage({
        type: 'requirementClear',
        sessionId: shared.sessionId,
        content: `好的，需求已明确: "${execRes.requirement}"，正在为您生成模型...`
      });
      return Actions.WriteTableDoc;
    }
  }
}

export class AskClarificationNode extends Node {
  constructor(maxRetries?: number) {
    super(maxRetries);
  }

  override async prep(
    shared: SharedStorage
  ): Promise<Pick<SharedStorage, "clarificationQuestions" | "panel" | "sessionId">> {
    console.log("Preparing to ask clarification questions...");
    return {
      clarificationQuestions: shared.clarificationQuestions,
      panel: shared.panel, // Pass panel and sessionId for communication
      sessionId: shared.sessionId,
    };
  }

  override async exec({
    clarificationQuestions,
    panel,
    sessionId,
  }: Pick<SharedStorage, "clarificationQuestions" | "panel" | "sessionId">): Promise<string> {
    if (!clarificationQuestions || clarificationQuestions.length === 0) {
      return "";
    }

    console.log("Sending clarification questions to WebView and awaiting answers...");

    // Send questions to WebView
    panel.webview.postMessage({
      type: 'askClarificationQuestionsInWebview',
      sessionId: sessionId,
      questions: clarificationQuestions
    });

    // Return a Promise that resolves when answers are received from the WebView
    return new Promise<string>((resolve, reject) => {
      const disposable = panel.webview.onDidReceiveMessage(message => {
        if (message.type === 'clarificationAnswersFromWebview' && message.sessionId === sessionId) {
         
          console.log('Received answers from WebView:', message.data);
         
          disposable.dispose(); // Clean up listener
          resolve(`Q:\n ${clarificationQuestions.join('\n')}\nA: \n${message.data}`);
        }
      });

      // Optional: Add a timeout or other error handling for the Promise
      // setTimeout(() => {
      //   disposable.dispose();
      //   reject(new Error('Timeout waiting for clarification answers from WebView'));
      // }, 60000); // 60 seconds timeout
    });
  }

  override async post(
    shared: SharedStorage,
    _prepRes: unknown,
    execRes: string // This will be the concatenated string of Q&A from WebView
  ): Promise<string | undefined> {
    shared.clarification += execRes;

    // Optionally, send a confirmation or the result back to WebView if needed
    shared.panel.webview.postMessage({
      type: 'clarificationProcessComplete',
      sessionId: shared.sessionId,
      payload: { receivedAnswers: execRes }
    });

    return Actions.AnalyzeRequirement;
  }
}

export class GenerateTableNode extends Node {
  constructor(maxRetries?: number) {
    super(maxRetries);
  }

  override async prep(
    shared: SharedStorage
  ): Promise<Pick<SharedStorage, "userRequirement" | "workspaceFolder" | "panel" |"sessionId">> {
    return {
      userRequirement: shared.userRequirement,
      workspaceFolder: shared.workspaceFolder,
      panel: shared.panel,
      sessionId: shared.sessionId,
    };
  }

  override async exec({
    userRequirement,
    workspaceFolder,
    panel,
    sessionId
  }: Pick<SharedStorage, "userRequirement" |"workspaceFolder" | "panel" | "sessionId">): Promise<string> {
    console.log("Generating table code...");

    const tableInfo = await getTableAndColumnInfo(workspaceFolder);
    const prompt = getGenerateTableCodePrompt(userRequirement, tableInfo);

    const result = streamText({
      model: deepseek("deepseek-chat"),
      prompt,
    });

    let fullText = "";
    // Stream text to webview
    for await (const textPart of result.textStream) {
      fullText += textPart;
      panel.webview.postMessage({
        type: 'llmChunk',
        sessionId: sessionId,
        content: textPart,
      });
      console.log('chunk send', textPart)
    }

    console.log(`Table generation stream finished. Full result: ${fullText}`);

    // After streaming is complete, send a final message indicating completion
    return fullText;
  }

  override async post(
    shared: SharedStorage,
    _prepRes: unknown,
    execRes: string
  ): Promise<string | undefined> {
    // 将生成的 TypeScript 实体类转换为 ER 图数据结构
    const schema = parseEntityToSchema(execRes);
    shared.dbTableDoc = JSON.stringify(schema);
  
    console.log("Table code generated successfully. ====>", schema);
    // 发送 ER 图数据到 webview
    shared.panel.webview.postMessage({
      type: 'updateERDiagramContent',
      content: JSON.stringify({
        name: 'public',
        tables: (schema ?? []).map(table => {
          return {
            name: table.name,
            type: 'table',
            columns: table.columns
          }
        }),
      })
    });

    return;
  }
}