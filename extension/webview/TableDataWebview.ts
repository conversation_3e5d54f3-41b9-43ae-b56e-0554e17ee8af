import * as vscode from "vscode";
import { ConnectionManager } from "../utils/connection/connection-manager";

export class TableDataWebviewContainer {
  private openPanels: Map<string, vscode.WebviewPanel> = new Map();

  constructor(
    private context: vscode.ExtensionContext,
    private connectionManager: ConnectionManager
  ) {}

  /**
   * 生成表格的唯一标识符
   */
  private getTableKey(
    connectionConfigId: string,
    databaseName: string,
    tableName: string,
    schemaName?: string
  ): string {
    return `${connectionConfigId}|${databaseName}|${
      schemaName || ""
    }|${tableName}`;
  }

  /**
   * Shows the data of a table in a new tab
   * @param connectionId The ID of the connection
   * @param databaseName The name of the database
   * @param tableName The name of the table
   * @param schemaName Optional schema name
   */
  public async showTableData(
    connectionId: string,
    databaseName: string,
    tableName: string,
    schemaName?: string
  ): Promise<void> {
    try {
      console.debug(
        `Showing data for table ${databaseName}.${
          schemaName ? schemaName + "." : ""
        }${tableName}`
      );

      // 生成表格的唯一标识符
      const tableKey = this.getTableKey(
        connectionId,
        databaseName,
        tableName,
        schemaName
      );

      // 检查表格是否已经打开
      const existingPanel = this.openPanels.get(tableKey);
      if (existingPanel) {
        // 如果已经打开，则激活该面板
        console.debug(`Table ${tableName} is already open, activating panel`);
        existingPanel.reveal(vscode.ViewColumn.Active);
        return;
      }

      // Get the connection to determine database type
      const connectionConfig = await this.connectionManager.getConnectionConfig(
        connectionId
      );
      if (!connectionConfig) {
        throw new Error(`Connection with ID ${connectionId} not found`);
      }

      // Generate a SELECT query for the table based on database type
      let query: string;

      switch (connectionConfig.type) {
        case "PostgreSQL":
          query = `SELECT * FROM ${
            schemaName ? `"${schemaName}".` : ""
          }"${tableName}" ;`;
          break;
        case "MySQL":
          query = `SELECT * FROM ${
            schemaName ? `\`${schemaName}\`.` : ""
          }\`${tableName}\`;`;
          break;
        case "SQLite":
          query = `SELECT * FROM ${
            schemaName ? `"${schemaName}".` : ""
          }"${tableName}";`;
          break;
        default:
          query = `SELECT * FROM ${
            schemaName ? `${schemaName}.` : ""
          }${tableName};`;
      }

      await vscode.window.withProgress(
        {
          location: vscode.ProgressLocation.Notification,
          title: `Loading data from ${tableName}...`,
          cancellable: false,
        },
        async (_progress) => {
          try {
            console.debug(`Executing query: ${query}`);

            // Execute the query
            const result = await this.connectionManager.executeQuery(
              connectionId,
              query,
              databaseName,
              schemaName
            );

            console.debug(
              `Query executed successfully. Rows returned: ${result.rowCount}`
            );

            // Create HTML content for the table data
            const htmlContent = this.generateHtmlTable(result, tableName);

            // Create a WebviewPanel with the table name as title
            const panel = vscode.window.createWebviewPanel(
              "tableData", // Identifies the type of the webview
              tableName, // Title of the panel displayed to the user
              vscode.ViewColumn.Active, // 使用当前活动的编辑器列，而不是拆分
              {
                // Webview options
                enableScripts: true,
                retainContextWhenHidden: true,
              }
            );

            // 设置面板关闭时的处理函数
            panel.onDidDispose(() => {
              this.openPanels.delete(tableKey);
              console.debug(`Panel for table ${tableName} was closed`);
            });

            // 将面板添加到已打开面板的映射中
            this.openPanels.set(tableKey, panel);

            // 设置消息处理函数，处理来自 WebView 的消息
            panel.webview.onDidReceiveMessage(async (message) => {
              if (message.command === "refresh") {
                try {
                  console.debug(`Refreshing data for table ${tableName}`);

                  // 重新执行查询
                  const refreshedResult =
                    await this.connectionManager.executeQuery(
                      connectionId,
                      query,
                      databaseName,
                      schemaName
                    );

                  // 更新 HTML 内容
                  panel.webview.html = this.generateHtmlTable(
                    refreshedResult,
                    tableName
                  );

                  // 显示成功消息
                  vscode.window.showInformationMessage(
                    `Refreshed ${refreshedResult.rowCount} rows from ${tableName}`
                  );
                } catch (error) {
                  console.error(
                    `Failed to refresh table data: ${
                      error instanceof Error ? error.message : String(error)
                    }`
                  );
                  vscode.window.showErrorMessage(
                    `Failed to refresh table data: ${
                      error instanceof Error ? error.message : String(error)
                    }`
                  );
                }
              }
            });

            // Set the HTML content
            panel.webview.html = htmlContent;

            // Show a success message
            vscode.window.showInformationMessage(
              `Loaded ${result.rowCount} rows from ${tableName}`
            );
          } catch (error) {
            console.error(
              `Failed to load table data: ${
                error instanceof Error ? error.message : String(error)
              }`
            );
            // Show an error message
            vscode.window.showErrorMessage(
              `Failed to load table data: ${
                error instanceof Error ? error.message : String(error)
              }`
            );
          }
        }
      );
    } catch (error) {
      vscode.window.showErrorMessage(
        `Failed to show table data: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }
  }

  /**
   * Generates HTML content for displaying the table data
   */
  private generateHtmlTable(
    result: {
      columns: string[];
      rows: any[];
      rowCount: number;
      executionTime: number;
    },
    tableName: string
  ): string {
    // Create HTML content
    let html = `
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; img-src data:; script-src 'unsafe-inline';">
<title>${tableName}</title>
<style>
    :root {
        --container-padding: 20px;
        --input-padding-vertical: 6px;
        --input-padding-horizontal: 4px;
        --input-margin-vertical: 4px;
        --input-margin-horizontal: 0;
    }

    body {
        padding: 0 var(--container-padding);
        color: var(--vscode-foreground);
        font-size: var(--vscode-font-size);
        font-weight: var(--vscode-font-weight);
        font-family: var(--vscode-font-family);
        background-color: var(--vscode-editor-background);
    }

    h1 {
        margin-top: 20px;
        margin-bottom: 10px;
        font-weight: 600;
        line-height: 1.25;
        color: var(--vscode-foreground);
    }

    .info {
        margin-bottom: 20px;
        color: var(--vscode-descriptionForeground);
    }

    .table-container {
        overflow: auto;
        max-height: calc(100vh - 150px);
        border: 1px solid var(--vscode-panel-border);
    }

    table {
        border-collapse: collapse;
        width: 100%;
        font-size: var(--vscode-font-size);
    }

    th, td {
        padding: 8px 12px;
        text-align: left;
        border-bottom: 1px solid var(--vscode-panel-border);
        white-space: nowrap;
    }

    th {
        background-color: var(--vscode-editor-background);
        color: var(--vscode-foreground);
        font-weight: 600;
        position: sticky;
        top: 0;
        z-index: 1;
        border-bottom: 2px solid var(--vscode-panel-border);
    }

    tr:hover {
        background-color: var(--vscode-list-hoverBackground);
    }

    .null-value {
        color: var(--vscode-disabledForeground);
        font-style: italic;
    }

    .number-cell {
        text-align: right;
    }

    .date-cell {
        white-space: nowrap;
    }

    .controls {
        margin: 20px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .pagination {
        display: flex;
        align-items: center;
    }

    button {
        padding: 6px 14px;
        border: none;
        color: var(--vscode-button-foreground);
        background-color: var(--vscode-button-background);
        cursor: pointer;
        border-radius: 2px;
        margin-right: 8px;
    }

    button:hover {
        background-color: var(--vscode-button-hoverBackground);
    }

    button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .search-box {
        padding: 6px 10px;
        border: 1px solid var(--vscode-input-border);
        background-color: var(--vscode-input-background);
        color: var(--vscode-input-foreground);
        border-radius: 2px;
    }
</style>
</head>
<body>
<h1>${tableName}</h1>
<div class="info">
    Rows: ${result.rowCount}, Execution time: ${result.executionTime || 0}ms
</div>
<div class="controls">
    <div class="search">
        <input type="text" class="search-box" id="searchInput" placeholder="Search..." />
    </div>
    <div class="actions">
        <button id="refreshButton" title="Refresh data">Refresh</button>
    </div>
</div>
<div class="table-container">
    <table id="dataTable">
        <thead>
            <tr>
                ${result.columns
                  .map((col) => `<th>${this.escapeHtml(col)}</th>`)
                  .join("")}
            </tr>
        </thead>
        <tbody>
`;

    // Add table rows
    for (const row of result.rows) {
      html += "<tr>";
      for (const col of result.columns) {
        const value = row[col];
        if (value === null || value === undefined) {
          html += '<td class="null-value">NULL</td>';
        } else if (typeof value === "number") {
          html += `<td class="number-cell">${this.escapeHtml(
            this.formatValue(value)
          )}</td>`;
        } else if (value instanceof Date) {
          html += `<td class="date-cell">${this.escapeHtml(
            this.formatValue(value)
          )}</td>`;
        } else {
          html += `<td>${this.escapeHtml(this.formatValue(value))}</td>`;
        }
      }
      html += "</tr>\n";
    }

    html += `
        </tbody>
    </table>
</div>

<script>
    (function() {
        // 与 VSCode 通信
        const vscode = acquireVsCodeApi();

        // 搜索功能
        const searchInput = document.getElementById('searchInput');
        const dataTable = document.getElementById('dataTable');
        const rows = Array.from(dataTable.querySelectorAll('tbody tr'));

        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });

        // 刷新按钮事件
        const refreshButton = document.getElementById('refreshButton');
        refreshButton.addEventListener('click', function() {
            // 发送刷新命令给 VSCode
            vscode.postMessage({
                command: 'refresh'
            });

            // 显示加载中状态
            refreshButton.disabled = true;
            refreshButton.textContent = 'Refreshing...';

            // 3秒后恢复按钮状态（如果没有收到新数据）
            setTimeout(() => {
                refreshButton.disabled = false;
                refreshButton.textContent = 'Refresh';
            }, 3000);
        });

        // 根据内容类型添加类
        rows.forEach(row => {
            const cells = Array.from(row.querySelectorAll('td'));
            cells.forEach(cell => {
                // 检查单元格是否包含数字
                if (!isNaN(cell.textContent) && cell.textContent.trim() !== '') {
                    cell.classList.add('number-cell');
                }

                // 检查单元格是否包含日期
                const datePattern = /^\d{4}-\d{2}-\d{2}|\d{2}\/\d{2}\/\d{4}/;
                if (datePattern.test(cell.textContent)) {
                    cell.classList.add('date-cell');
                }
            });
        });
    })();
</script>
</body>
</html>
`;

    return html;
  }

  /**
   * Formats a value for display
   */
  private formatValue(value: any): string {
    if (value === null || value === undefined) {
      return "NULL";
    }

    if (typeof value === "object") {
      if (value instanceof Date) {
        return value.toISOString();
      }

      return JSON.stringify(value);
    }

    return String(value);
  }

  /**
   * Escapes HTML special characters
   */
  private escapeHtml(unsafe: string): string {
    return unsafe
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#039;");
  }
}
