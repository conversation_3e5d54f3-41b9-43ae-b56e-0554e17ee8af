import * as vscode from 'vscode';
import { ConnectionManager } from '../utils/connection/connection-manager';

export class ConnectionTreeItem extends vscode.TreeItem {
    constructor(
        public readonly label: string,
        public readonly collapsibleState: vscode.TreeItemCollapsibleState,
        public readonly connectionId?: string,
        public readonly contextValue?: string,
        public readonly databaseName?: string,
        public readonly schemaName?: string,
        public readonly tableName?: string
    ) {
        super(label, collapsibleState);

        // Set the context value for context menu filtering
        this.contextValue = contextValue;

        // Set tooltip
        if (connectionId) {
            this.tooltip = `${label} (${connectionId})`;
        } else {
            this.tooltip = label;
        }

        // Set icons based on item type
        if (contextValue === 'connection') {
            this.iconPath = new vscode.ThemeIcon('database');
        } else if (contextValue === 'database') {
            this.iconPath = new vscode.ThemeIcon('package');
        } else if (contextValue === 'schema') {
            this.iconPath = new vscode.ThemeIcon('symbol-namespace');
            
            // 添加 ER 图快捷方式图标
            if (connectionId && databaseName && schemaName) {
                this.resourceUri = vscode.Uri.parse(`command:dev-expert.showSchemaERDiagram?${encodeURIComponent(JSON.stringify({
                    connectionId,
                    databaseName,
                    schemaName
                }))}`);
                this.tooltip = '点击查看 ER 图';
            }
        } else if (contextValue === 'table') {
            this.iconPath = new vscode.ThemeIcon('table');

            // Add command to show table data when clicked
            if (connectionId && databaseName && tableName) {
                this.command = {
                    command: 'dev-expert.showTableData',
                    title: 'Show Table Data',
                    arguments: [{
                        connectionId,
                        databaseName,
                        tableName,
                        schemaName
                    }]
                };
            }
        } else if (contextValue === 'view') {
            this.iconPath = new vscode.ThemeIcon('eye');

            // Add command to show view data when clicked (same as table)
            if (connectionId && databaseName && tableName) {
                this.command = {
                    command: 'dev-expert.showTableData',
                    title: 'Show View Data',
                    arguments: [{
                        connectionId,
                        databaseName,
                        tableName,
                        schemaName
                    }]
                };
            }
        } else if (contextValue === 'column') {
            this.iconPath = new vscode.ThemeIcon('symbol-field');
        }
    }
}

export class DatabaseTreeDataProvider implements vscode.TreeDataProvider<ConnectionTreeItem> {
    private _onDidChangeTreeData: vscode.EventEmitter<ConnectionTreeItem | undefined | null | void> = new vscode.EventEmitter<ConnectionTreeItem | undefined | null | void>();
    readonly onDidChangeTreeData: vscode.Event<ConnectionTreeItem | undefined | null | void> = this._onDidChangeTreeData.event;

    constructor(private connectionManager: ConnectionManager) { }

    refresh(element?: ConnectionTreeItem): void {
        console.debug(`刷新连接视图 ${element ? `元素: ${element.label}` : '所有元素'}`);

        // 强制视图刷新
        this._onDidChangeTreeData.fire(undefined);

        // 显示当前连接状态
        const connections = this.connectionManager.getConnections();
        console.debug(`刷新后的连接数量: ${connections.length}`);
        if (connections.length > 0) {
            console.debug(`刷新后的连接列表: ${connections.map(c => c.name).join(', ')}`);
        }
    }

    getTreeItem(element: ConnectionTreeItem): vscode.TreeItem {
        return element;
    }

    async getChildren(element?: ConnectionTreeItem): Promise<ConnectionTreeItem[]> {
        // Root - all connections
        if (!element) {
            const connections = this.connectionManager.getConnections();
            console.debug(`获取所有连接: ${JSON.stringify(connections.map(c => ({ id: c.id, name: c.name, type: c.type })))}`);

            if (connections.length === 0) {
                console.debug('没有找到连接');
                return [
                    new ConnectionTreeItem(
                        '没有找到连接。点击 + 按钮添加连接。',
                        vscode.TreeItemCollapsibleState.None
                    )
                ];
            }

            console.debug(`返回连接列表: ${connections.map(c => c.name).join(', ')}`);
            return connections.map(connection =>
                new ConnectionTreeItem(
                    connection.name,
                    vscode.TreeItemCollapsibleState.Collapsed,
                    connection.id,
                    'connection'
                )
            );
        }

        // Connection 子元素 - 展示所有 databases
        if (element.contextValue === 'connection' && element.connectionId) {
            try {
                const databases = await this.connectionManager.getDatabases(element.connectionId);
                const connectionConfig = this.connectionManager.getConnectionConfig(element.connectionId);
                
                // 过滤掉未配置的数据库
                const configuredDatabases = databases.filter(database => {
                    // 如果连接配置中指定了数据库，则只显示该数据库
                    if (connectionConfig?.database) {
                        return database.name === connectionConfig.database;
                    }
                    // 如果没有配置数据库，则显示所有数据库
                    return true;
                });

                return configuredDatabases.map(database =>
                    new ConnectionTreeItem(
                        database.name,
                        vscode.TreeItemCollapsibleState.Collapsed,
                        element.connectionId,
                        'database',
                        database.name
                    )
                );
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to get databases: ${error instanceof Error ? error.message : String(error)}`);
                return [
                    new ConnectionTreeItem(
                        'Error loading databases. Click refresh to try again.',
                        vscode.TreeItemCollapsibleState.None
                    )
                ];
            }
        }

        // Database 子元素 - 展示 schemas 或者 tables
        if (element.contextValue === 'database' && element.connectionId && element.databaseName) {
            try {
                const connectionConfig = this.connectionManager.getConnectionConfig(element.connectionId);

                // For PostgreSQL and SQL Server, show schemas
                if (connectionConfig && (connectionConfig.type === 'PostgreSQL')) {
                    const schemas = await this.connectionManager.getSchemas(element.connectionId, element.databaseName);

                    return schemas.map(schema =>
                        new ConnectionTreeItem(
                            schema.name,
                            vscode.TreeItemCollapsibleState.Collapsed,
                            element.connectionId,
                            'schema',
                            element.databaseName,
                            schema.name
                        )
                    );
                }
                // For other databases, show tables directly
                else {
                    const tables = await this.connectionManager.getTables(element.connectionId, element.databaseName);

                    return tables.map(table =>
                        new ConnectionTreeItem(
                            table.name,
                            vscode.TreeItemCollapsibleState.Collapsed,
                            element.connectionId,
                            table.type === 'table' ? 'table' : 'view',
                            element.databaseName,
                            undefined,
                            table.name
                        )
                    );
                }
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to get database objects: ${error instanceof Error ? error.message : String(error)}`);
                return [
                    new ConnectionTreeItem(
                        'Error loading database objects. Click refresh to try again.',
                        vscode.TreeItemCollapsibleState.None
                    )
                ];
            }
        }

        // Schema 子元素 - 展示 tables
        if (element.contextValue === 'schema' && element.connectionId && element.databaseName && element.schemaName) {
            try {
                const tables = await this.connectionManager.getTables(
                    element.connectionId,
                    element.databaseName,
                    element.schemaName
                );

                return tables.map(table =>
                    new ConnectionTreeItem(
                        table.name,
                        vscode.TreeItemCollapsibleState.Collapsed,
                        element.connectionId,
                        table.type === 'table' ? 'table' : 'view',
                        element.databaseName,
                        element.schemaName,
                        table.name
                    )
                );
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to get tables: ${error instanceof Error ? error.message : String(error)}`);
                return [
                    new ConnectionTreeItem(
                        'Error loading tables. Click refresh to try again.',
                        vscode.TreeItemCollapsibleState.None
                    )
                ];
            }
        }

        // Table 子元素 - 展示 columns
        if ((element.contextValue === 'table' || element.contextValue === 'view') &&
            element.connectionId && element.databaseName && element.tableName) {
            try {
                const columns = await this.connectionManager.getColumns(
                    element.connectionId,
                    element.databaseName,
                    element.tableName,
                    element.schemaName
                );

                return columns.map(column => {
                    const label = `${column.name} (${column.type}${column.isPrimaryKey ? ', PK' : ''}${column.isForeignKey ? ', FK' : ''})`;

                    return new ConnectionTreeItem(
                        label,
                        vscode.TreeItemCollapsibleState.None,
                        element.connectionId,
                        'column',
                        element.databaseName,
                        element.schemaName,
                        element.tableName
                    );
                });
            } catch (error) {
                vscode.window.showErrorMessage(`Failed to get columns: ${error instanceof Error ? error.message : String(error)}`);
                return [
                    new ConnectionTreeItem(
                        'Error loading columns. Click refresh to try again.',
                        vscode.TreeItemCollapsibleState.None
                    )
                ];
            }
        }

        return [];
    }
}
