import * as vscode from 'vscode'
import { getHtmlForWebview } from './utils'
import * as fs from 'fs'
import { config } from 'dotenv'
import path from 'path'
import { createCodeAgengFlow } from '../utils/code-agent/flow'
import { CodingAgentSharedStore } from '../utils/code-agent/types'
import { Logger as AILogger } from '../utils/code-agent/utils/logger'
import { McpHub } from '../services/mcp/McpHub'
import { fileExistsAtPath } from '../utils/fs'
import { Flow } from 'pocketflow'
import { messageCenter, MessageEvent } from '../utils/messageCenter'

interface OpenAIResponse {
    choices: Array<{
        message: {
            content: string
        }
    }>
}

export class AIChatWebviewProvider implements vscode.WebviewViewProvider {
    public static readonly viewType = 'aiChatView'
    private _view?: vscode.WebviewView
    private codeAgentFlow: Flow<CodingAgentSharedStore> | null = null

    constructor(private readonly _context: vscode.ExtensionContext) {
        this.loadUserProjectEnv()

        // 监听主题变化
        vscode.window.onDidChangeActiveColorTheme(() => {
            this.sendThemeToWebview()
        })
    }

    private loadUserProjectEnv() {
        try {
            // 获取当前工作区的根目录
            const workspaceFolders = vscode.workspace.workspaceFolders
            if (workspaceFolders && workspaceFolders.length > 0) {
                const workspaceRoot = workspaceFolders[0].uri.fsPath

                // 定义环境文件的优先级顺序（后面的会覆盖前面的）
                const envFiles = [
                    '.env.local', // 本地环境文件
                    '.env', // 基础环境文件
                    '.env.development', // 开发环境文件
                    '.env.development.local', // 本地开发环境文件
                ]

                let loadedFiles: string[] = []

                // 按顺序加载环境文件
                for (const envFile of envFiles) {
                    const envPath = path.join(workspaceRoot, envFile)

                    if (fs.existsSync(envPath)) {
                        try {
                            // 加载环境文件到process.env
                            config({ path: envPath, override: false }) // override: false 表示不覆盖已存在的环境变量
                            loadedFiles.push(envFile)
                            console.log(`已加载环境文件: ${envPath}`)
                        } catch (error) {
                            console.error(`加载环境文件 ${envFile} 时出错:`, error)
                        }
                    }
                }

                if (loadedFiles.length > 0) {
                    console.log(`成功加载的环境文件: ${loadedFiles.join(', ')}`)
                } else {
                    console.log('用户项目目录下未找到任何环境文件')
                }
            } else {
                console.log('未找到工作区目录')
            }
        } catch (error) {
            console.error('加载环境文件时出错:', error)
        }
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        this._view = webviewView

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [vscode.Uri.joinPath(this._context.extensionUri, 'dist')],
        }

        webviewView.webview.html = this._getHtmlForWebview(webviewView.webview)

        webviewView.webview.onDidReceiveMessage(async message => {
            await this.handleMessage(message)
        })

        // 发送初始主题信息
        setTimeout(() => {
            this.sendThemeToWebview()
        }, 100)
    }

    private async handleMessage(message: any) {
        console.log('AI Chat View message:', message)

        switch (message.type) {
            case 'sendMessage':
                this.handleUserMessage(message.content)
                break
            case 'getTheme':
                this.handleGetTheme()
                break
            case 'newChat':
                this.handleNewChat()
                break
            default:
                console.log('Unknown message type:', message.type)
        }
    }

    private clearCodeAgent() {
        this.codeAgentFlow = null
    }

    private handleNewChat() {
        messageCenter.sendMessage(MessageEvent.NEW_CHAT_REQUESTED)
        // 清理当前的代码代理流程
        this.clearCodeAgent()

        // 可以在这里添加其他新建聊天的逻辑
        // 比如记录聊天历史、重置状态等
        console.log('New chat session started')
    }

    private async handleUserMessage(userMessage: string) {
        if (!this._view) return
        if (this.codeAgentFlow) {
            messageCenter.sendMessage(MessageEvent.USER_MESSAGE_RECEIVED, userMessage)
            return
        }
        this.createCodeAgentAndRun(userMessage)
    }

    private async createCodeAgentAndRun(userMessage: string) {
        // 获取OpenAI API密钥
        const config = vscode.workspace.getConfiguration('flowEditor')

        try {
            // 现在可以直接从process.env读取环境变量
            const model = process.env.LLM_MODEL_NAME
            const apiKey = process.env.LLM_API_KEY
            const baseURL = process.env.LLM_BASE_URL

            const codeAgentFlow = createCodeAgengFlow()
            this.codeAgentFlow = codeAgentFlow

            const postMessage = (message: string) => {
                if (this._view) {
                    this._view.webview.postMessage({
                        type: 'aiResponse',
                        content: message,
                    })
                }
            }

            if (!model) {
                postMessage('请在设置中配置 LLM 模型名称。')
                return
            }
            if (!baseURL) {
                postMessage('请在设置中配置 LLM 基础 URL。')
                return
            }
            if (!apiKey && !process.env.LLM_API_KEY) {
                postMessage('请在设置中配置 OpenAI API 密钥。')
                return
            }

            const workingDir = vscode.workspace.workspaceFolders?.[0].uri.fsPath

            if (!workingDir) {
                postMessage('请打开一个工作区文件夹。')
                return
            }

            const isMCPSettingFileExists = await fileExistsAtPath(
                path.resolve(workingDir, 'mcp_settings.json'),
            )

            const shared: CodingAgentSharedStore = {
                userQuery: userMessage,
                history: [],
                workingDir, // 使用当前目录作为工作目录
                editOperations: [],
                currentAction: null,
                mcpHub: isMCPSettingFileExists
                    ? new McpHub(async () => await path.resolve(workingDir, 'mcp_settings.json'))
                    : undefined,
                output: {
                    text: (text: string) => {
                        postMessage(text)
                    },
                },
            }

            codeAgentFlow.run(shared)
        } catch (error) {
            console.error('处理用户消息时出错:', error)
            if (this._view) {
                this._view.webview.postMessage({
                    type: 'aiResponse',
                    content: '抱歉，处理您的消息时出现错误。请检查网络连接和API密钥配置。',
                })
            }
        }
    }

    private _getHtmlForWebview(webview: vscode.Webview) {
        const distHtmlPath = vscode.Uri.joinPath(
            this._context.extensionUri,
            'dist',
            'chat',
            'index.html',
        )
        return getHtmlForWebview(distHtmlPath, webview, this._context.extensionUri)
    }

    public postMessage(message: any) {
        if (this._view) {
            this._view.webview.postMessage(message)
        }
    }

    private handleGetTheme() {
        this.sendThemeToWebview()
    }

    private sendThemeToWebview() {
        if (!this._view) return

        const currentTheme = vscode.window.activeColorTheme
        let themeKind: string

        switch (currentTheme.kind) {
            case vscode.ColorThemeKind.Light:
                themeKind = 'light'
                break
            case vscode.ColorThemeKind.Dark:
                themeKind = 'dark'
                break
            case vscode.ColorThemeKind.HighContrast:
                themeKind = 'high-contrast'
                break
            default:
                themeKind = 'dark'
        }

        this._view.webview.postMessage({
            type: 'themeChanged',
            theme: themeKind,
        })
    }
}
