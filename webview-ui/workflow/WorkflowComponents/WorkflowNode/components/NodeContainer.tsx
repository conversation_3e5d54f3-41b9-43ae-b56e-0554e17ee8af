import React, { useMemo } from "react";
import { NodeContainerProps } from "../interface";
import { Box, Button, Flex } from "@radix-ui/themes";
import { number2px } from "../../../utils/number2px";
import { Handle, Position } from "@xyflow/react";
import  {BookmarkIcon, ChevronDownIcon, Pencil2Icon} from '@radix-ui/react-icons'



const NodeContainer: React.FC<NodeContainerProps> = (props) => {
  const {
    children,
    icon,
    label = 'unknown',
    intro,
    minWidth = '300px',
    maxWidth = '666px',
    minHeight,
    maxHeight,
    width = 'full',
    height = 'full',
    nodeId,
    selected,
    hasCustomHandle = false,
    isError = false,
    isFolded = false,
    isSelected = false,
    customStyle
  } = props;

  const renderHeader =() => {
    return <Box  px="3" pt="4">
      <Flex align="center" mb="1" gap="2">
        <ChevronDownIcon/>
        {icon ? <img src={icon} alt="icon" className="w-4 h-4" /> : null}
        <span className="text-lg font-bold">{label}</span>
        <Button variant="ghost" size="1">
          <Pencil2Icon color="gray" className="w-4 h-4"/>
        </Button>
      </Flex>
      <Box className="text-sm text-gray-500">{intro ?? '该节点没有说明'}</Box>
    </Box>
  }

  const renderHandle = () => {
    return <>
      <Handle type="target" position={Position.Left} />
      <Handle type="source" position={Position.Right} />
    </>
  }

  const render = () => {
    return <Flex 
      direction="column"
      minHeight={number2px(minHeight)} 
      height={number2px(height)} 
      maxHeight={number2px(maxHeight)}
      width={number2px(width)} 
      minWidth={number2px(minWidth)} 
      maxWidth={number2px(maxWidth)} 
      className={`bg-white shadow-lg ${selected ? ' outline outline-2 outline-gray-300': 'outline outline-1 outline-gray-300'} rounded-md`}
      >
        {renderHeader()}
        {!hasCustomHandle && renderHandle() }
      <div className="nodrag nowheel flex flex-col flex-1 my-3 gap-1"  >
        {!isFolded ? children : <Box height="4" />}
      </div>
    </Flex>
  }

  return render();
};

export default React.memo(NodeContainer);
