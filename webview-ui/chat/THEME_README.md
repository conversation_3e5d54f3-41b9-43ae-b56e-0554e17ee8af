# VS Code 主题适配说明

本项目已经实现了与 VS Code 主题的自动适配功能，可以根据用户当前的 VS Code 主题自动调整界面的背景色和文字颜色。

## 实现原理

### 1. CSS 变量系统

在 `index.css` 中定义了完整的 VS Code 主题变量系统：

- **默认深色主题**：`:root` 选择器中定义的默认变量
- **浅色主题**：`[data-vscode-theme-kind="vscode-light"]` 选择器中的变量覆盖
- **高对比度主题**：`[data-vscode-theme-kind="vscode-high-contrast"]` 选择器中的变量覆盖

### 2. 主题检测脚本

`theme-detector.js` 负责：

- 检测当前 VS Code 主题类型
- 监听主题变化事件
- 动态应用对应的主题类名
- 回退到系统主题检测

### 3. 组件样式适配

所有组件都已经从硬编码的颜色值改为使用 CSS 变量：

- `App.tsx`：主容器背景和文字颜色
- `ChatHeader.tsx`：标题栏样式
- `MessageList.tsx`：消息气泡和加载指示器
- `ChatInput.tsx`：输入框、选择器和按钮
- `QuickActions.tsx`：快捷操作按钮

## 支持的主题变量

### 背景色
- `--vscode-editor-background`：主编辑器背景
- `--vscode-sideBar-background`：侧边栏背景
- `--vscode-input-background`：输入框背景
- `--vscode-button-background`：按钮背景

### 文字颜色
- `--vscode-editor-foreground`：主文字颜色
- `--vscode-sideBar-foreground`：侧边栏文字颜色
- `--vscode-input-foreground`：输入框文字颜色
- `--vscode-descriptionForeground`：次要文字颜色

### 边框和交互
- `--vscode-panel-border`：边框颜色
- `--vscode-input-border`：输入框边框
- `--vscode-list-hoverBackground`：悬停背景
- `--vscode-list-activeSelectionBackground`：选中背景

## 自定义 CSS 类

为了方便使用，定义了以下 CSS 类：

- `.vscode-bg-primary`：主背景色
- `.vscode-bg-secondary`：次要背景色
- `.vscode-text-primary`：主文字颜色
- `.vscode-text-secondary`：次要文字颜色
- `.vscode-text-muted`：弱化文字颜色
- `.vscode-border`：边框颜色
- `.vscode-input`：输入框样式
- `.vscode-button`：按钮样式
- `.vscode-hover`：悬停效果
- `.vscode-active`：激活状态

## 使用方法

### 在组件中使用

```tsx
// 使用 CSS 类
<div className="vscode-bg-primary vscode-text-primary">
  内容
</div>

// 使用 CSS 变量
<div style={{
  backgroundColor: 'var(--vscode-editor-background)',
  color: 'var(--vscode-editor-foreground)'
}}>
  内容
</div>
```

### 扩展主题支持

如果需要添加新的主题变量：

1. 在 `index.css` 的三个主题选择器中添加变量定义
2. 创建对应的 CSS 类（可选）
3. 在组件中使用新变量

## 主题检测机制

主题检测按以下优先级进行：

1. **VS Code API**：通过 `acquireVsCodeApi()` 获取主题信息
2. **DOM 类名**：检查 body 元素的 VS Code 主题类名
3. **背景色分析**：分析计算样式中的背景色亮度
4. **系统主题**：回退到系统的 `prefers-color-scheme` 设置

## 注意事项

1. **主题变量优先级**：CSS 变量会根据选择器优先级自动覆盖
2. **回退机制**：如果无法检测到 VS Code 主题，会使用系统主题或默认深色主题
3. **动态更新**：主题变化时会自动更新，无需刷新页面
4. **兼容性**：支持所有现代浏览器，CSS 变量有良好的兼容性

## 测试主题切换

在 VS Code 中测试主题适配：

1. 打开命令面板（Cmd/Ctrl + Shift + P）
2. 输入 "Preferences: Color Theme"
3. 选择不同的主题（如 Dark+、Light+、High Contrast）
4. 观察 webview 界面是否自动适配

## 故障排除

如果主题没有正确应用：

1. 检查浏览器控制台是否有 JavaScript 错误
2. 确认 `theme-detector.js` 已正确加载
3. 检查 body 元素是否有正确的 `data-vscode-theme-kind` 属性
4. 验证 CSS 变量是否正确定义和应用