import React, { useState, useRef, useEffect } from "react";
import { Button } from "@radix-ui/themes";
import { useAnalyzeRequirement } from "./useAnalyzeRequirement";

export const ChatPanel: React.FC = () => {
  const {sessionId, messages, loading, onInputChange, onCreate, onSend} = useAnalyzeRequirement();
  const [input, setInput] = useState("");
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null); // 新增：用于消息列表滚动的ref

  const handleSend = () => {
    onSend()
    setInput("");
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInput(e.target.value);
    onInputChange(e.target.value);
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      const maxHeight = window.innerHeight * 0.5; // 例如，最大高度为视窗高度的一半
      textareaRef.current.style.height =
        Math.min(textareaRef.current.scrollHeight, maxHeight) + "px";
    }
  };

  // 调节输入框高度
  React.useEffect(() => {
    if (textareaRef.current) {
      // 仅在非澄清模式下调整输入框高度
      textareaRef.current.style.height = "auto";
      const maxHeight = window.innerHeight * 0.5;
      textareaRef.current.style.height =
        Math.min(textareaRef.current.scrollHeight, maxHeight) + "px";
    }
  }, [input]); 

  // 新增：每次消息更新时滚动到底部
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollTop = messagesEndRef.current.scrollHeight;
    }
  }, [messages]);

  const handleNewSession = () => {
    setInput("");
    onCreate();
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.style.height = "auto"; // 重置高度
      }
    }, 0);
  };


  return (
    <div
      className="flex flex-col h-full border-l border-gray-200 bg-white"
      style={{width: '400px' }}
    >
      {/* 拖拽分割条 */}
      <div
        className="absolute left-0 top-0 h-full w-2 cursor-ew-resize z-50 bg-transparent hover:bg-blue-100 transition-colors duration-150"
        style={{ marginLeft: -2 }}
      />
      {/* 消息区 */}
      <div ref={messagesEndRef} className="flex-1 overflow-y-auto p-4 space-y-4 bg-gradient-to-b from-blue-50 to-white rounded-t-lg shadow-inner">
        {messages.map(
          (
            msg,
            idx // Added optional chaining for sessions[currentSessionIdx]
          ) => (
            <div
              key={`${sessionId}-msg-${idx}`}
              className="text-left"
            >
              {" "}
              {/* Improved key */}
              <div
                className={`
                inline-block rounded-2xl px-4 py-2 shadow max-w-[70%] break-words
                ${
                  msg.role === "user"
                    ? "bg-blue-500 text-white shadow-md"
                    : "bg-gray-200 text-gray-800"
                }
              `}
                style={{
                  borderBottomRightRadius: msg.role === "user" ? 0 : undefined,
                  borderBottomLeftRadius: msg.role === "bot" ? 0 : undefined,
                }}
              >
                {msg.content.split("\n").map((line, i) => (
                  <React.Fragment key={i}>
                    {line}
                    {i !== msg.content.split("\n").length - 1 && <br />}
                  </React.Fragment>
                ))}
              </div>
            </div>
          )
        )}
      </div>
      <div
          className="px-2 py-1 gap-1 border-t border-gray-100 bg-white rounded-b-lg shadow flex items-start"
          style={{ position: "sticky", bottom: 0 }}
        >
          <div className="flex gap-2 w-full items-end">
            <textarea
              ref={textareaRef}
              className="flex-1 border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-300 resize-none max-h-[50vh] min-h-[72px] transition-all duration-200 shadow-sm bg-gray-50"
              placeholder="在这里输入消息，按下 cmd/ctrl + ↵ 发送"
              value={input}
              onChange={handleInputChange}
              rows={3} // Default rows, height will adjust
              style={{ overflowY: "auto" }} // minHeight and maxHeight are handled by className or direct style if needed
              onKeyDown={(e) => {
                if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
                  e.preventDefault();
                  handleSend();
                }
              }}
            />
          </div>
          <div className="flex shrink-0 gap-1 flex-col justify-start">
            <Button size="2" onClick={handleSend} disabled={loading || !input.trim()}>
              发送
            </Button>{" "}
            {/* Disable send if input is empty */}
            <Button
              size="2"
              variant="soft"
              color="gray"
              onClick={handleNewSession}
            >
              新建
            </Button>
          </div>
      </div>

    </div>
  );
};
