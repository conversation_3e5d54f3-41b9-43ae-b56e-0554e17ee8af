import * as vscode from "vscode";
import { getHtmlForWebview } from "./utils";

export class UserProfileWebviewContainer {
    private panels: Map<string, vscode.WebviewPanel> = new Map();
    private readonly maxPanels: number = 3;

    constructor(private readonly context: vscode.ExtensionContext) {}

    public async show(): Promise<void> {
        try {
            console.debug(`showing login page`);

            const key = "lvjiawen.dev-expoert.login.html";
            const existingPanel = this.panels.get(key);

            if (existingPanel) {
                existingPanel.reveal(vscode.ViewColumn.Active);
                return;
            }

            // 超过最大数量，关闭最早的
            if (this.panels.size >= this.maxPanels) {
                const firstKey = this.panels.keys().next().value;
                this.panels.get(firstKey!)?.dispose();
                this.panels.delete(firstKey!);
            }

            const panel = this.createPanel();
            this.panels.set(key, panel);

            // 关闭时移除
            panel.onDidDispose(() => {
                this.panels.delete(key);
                console.debug(`Panel for login was closed`);
            });

            // 等待 webview 加载完成后发送数据
            panel.webview.onDidReceiveMessage(async (message) => {
                console.log("Received message from webview:", message);
            });
        } catch (error) {
            vscode.window.showErrorMessage(
                `Failed to show ER diagram: ${
                    error instanceof Error ? error.message : String(error)
                }`
            );
        }
    }

    private createPanel(): vscode.WebviewPanel {
        const panel = vscode.window.createWebviewPanel(
            "userProfile",
            `用户详情`,
            vscode.ViewColumn.Active,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(this.context.extensionUri, "dist"),
                ],
            }
        );

        panel.webview.html = getHtmlForWebview(
            vscode.Uri.joinPath(
                this.context.extensionUri,
                "dist",
                "user-profile",
                "index.html"
            ),
            panel.webview,
            this.context.extensionUri
        );
        return panel;
    }
}
