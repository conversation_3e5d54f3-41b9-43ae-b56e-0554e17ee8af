#!/usr/bin/env node

const cp = require('child_process');
const path = require('path');

// Output starting message for VS Code task system
process.stdout.write('Starting compilation\n');

// Run TypeScript compiler in watch mode
const tsc = cp.spawn('npx', [
  'tsc',
  '-watch',
  '-p',
  './extension/tsconfig.json'
], { shell: true });


// Run Vite build in watch mode for webview
const vite = cp.spawn('npx', [
  'vite',
  'build',
  '.',
  '--watch'
], { shell: true });

// Forward tsc stdout to parent process
tsc.stdout.on('data', (data) => {
  const output = data.toString();
  process.stdout.write(output.split('\n').map(line => line ? '[tsc] ' + line : '').join('\n'));

  // Check if compilation is complete
  if (output.includes('Watching for file changes')) {
    process.stdout.write('[tsc] Compilation complete\n');
  }
});

// Forward tsc stderr to parent process
tsc.stderr.on('data', (data) => {
  process.stderr.write('[tsc] ' + data.toString());
});

// Forward vite stdout to parent process
vite.stdout.on('data', (data) => {
  process.stdout.write('[vite] ' + data.toString());
});

// Forward vite stderr to parent process
vite.stderr.on('data', (data) => {
  process.stderr.write('[vite] ' + data.toString());
});

// Handle process exit
// 只要有一个子进程退出，主进程也退出
const exitHandler = (code) => {
  process.exit(code);
};
tsc.on('close', exitHandler);
vite.on('close', exitHandler);
