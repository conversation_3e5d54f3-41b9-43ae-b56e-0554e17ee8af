import { addEdge, Background, ReactFlow, Controls } from "@xyflow/react";
import React, { forwardRef, useCallback, useImperativeHandle } from "react";
import { createNewNode, nodeInfoMap } from "../WorkflowNode";
import { useAtom, useSet<PERSON>tom } from "jotai";
import {
  edges<PERSON>tom,
  nodesAtom,
  onEdgesChangeAtom,
  onNodesChangeAtom,
} from "./atoms";
import {
  WorkflowEdge,
  WorkflowNodeTypeEnum,
  ReactFlowNodeConfig,
} from "@lvjw/code-workflow-types";

const nodeTypes = nodeInfoMap.getNodeTypes();

export const WorkflowCanvas = forwardRef((props, ref) => {
  const [nodes, setNodes] = useAtom(nodesAtom);
  const [edges, setEdges] = useAtom(edgesAtom);

  const onNodesChange = useSetAtom(onNodesChangeAtom);
  const onEdgesChange = useSetAtom(onEdgesChangeAtom);

  const onConnect = useCallback(
    (params) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const addNode = (nodeType: WorkflowNodeTypeEnum) => {
    setNodes((nds) => nds.concat([createNewNode(nodeType, nds)]));
  };

  const getGrah = () => {
    return {
      nodes,
      edges,
    };
  };

  const setGraph = (graph: {
    nodes: ReactFlowNodeConfig[];
    edges: WorkflowEdge[];
  }) => {
    setNodes(graph.nodes);
    setEdges(graph.edges);
  };

  useImperativeHandle(ref, () => ({
    addNode,
    getGrah,
    setGraph,
  }));

  return (
    <div className="flex-1 justify-center items-center">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        nodeTypes={nodeTypes}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
      >
        <Background />
        <Controls />
      </ReactFlow>
    </div>
  );
});
