import { Node } from 'pocketflow'
import { readFile } from '../utils/readFile'
import { CodingAgentSharedStore, EditCodeAgentActionEnum, History, Output } from '../types'
import path from 'path'

interface PrepResult {
    filePath: string
    output: Output
}

export class ReadTargetFileNode extends Node<CodingAgentSharedStore> {
    async prep(shared: CodingAgentSharedStore): Promise<PrepResult> {
        const lastAction = shared.currentAction!
        const targetFile = lastAction.params.targetFile
        const filePath = path.resolve(shared.workingDir, targetFile)

        return { filePath, output: shared.output }
    }

    async exec(prepResult: PrepResult): Promise<{ content: string; success: boolean }> {
        const [success, content] = await readFile(prepResult.filePath, undefined, undefined, true)

        prepResult.output.text(
            `读取编辑目标文件 ${prepResult.filePath}${success ? '  成功' : '失败'}`,
        )

        return { content, success }
    }

    async post(
        shared: CodingAgentSharedStore,
        prepResult: PrepResult,
        execResult: { content: string; success: boolean },
    ) {
        shared.history.push({
            role: 'user',
            content: JSON.stringify({
                ...shared.currentAction!,
                result: execResult.content,
                success: execResult.success,
            }),
        })
        shared.currentAction = null

        return EditCodeAgentActionEnum.default
    }
}
