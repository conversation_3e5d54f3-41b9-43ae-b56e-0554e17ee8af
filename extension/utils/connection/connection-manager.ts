import * as vscode from 'vscode';
import { ConnectionProvider, DatabaseConnectionConfig } from "./interface";
import { PostgreSQLProvider } from './providers/postgresql-provider';

export class ConnectionManager {
    private context: vscode.ExtensionContext;

    private connectionConfigs: DatabaseConnectionConfig[] = [];
    private providers: Map<string, ConnectionProvider> = new Map();
    private activeConnections: Map<string, any> = new Map();
   

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        // 加载已经保存的连接
        this.loadConnections();

        // 初始化 providers
        this.providers.set('PostgreSQL', new PostgreSQLProvider());
    }

    /**
     * 从全局状态加载连接
     */
    private loadConnections(): void {
        this.connectionConfigs = this.context.globalState.get<DatabaseConnectionConfig[]>('connections', []);
        console.debug(`加载了 ${this.connectionConfigs.length} 个保存的连接`);

        if (this.connectionConfigs.length > 0) {
            console.debug(`连接列表: ${JSON.stringify(this.connectionConfigs.map(c => ({ id: c.id, name: c.name, type: c.type })))}`);
        }
    }

    public getConnections(): DatabaseConnectionConfig[] {
        console.debug(`获取连接列表，当前有 ${this.connectionConfigs.length} 个连接`);
        return [...this.connectionConfigs];
    }

    public getConnectionConfig(id: string): DatabaseConnectionConfig | undefined {
        return this.connectionConfigs.find(conn => conn.id === id);
    }

    public async addConnection(connection: Omit<DatabaseConnectionConfig, 'id'>): Promise<string> {
        const id = Date.now().toString();

        const newConnection: DatabaseConnectionConfig = {
            id,
            ...connection
        };

        this.connectionConfigs.push(newConnection);
        this.context.globalState.update('connections', this.connectionConfigs);
        console.debug(`当前连接数量: ${this.connectionConfigs.length}`);

        return id;
    }

    public async removeConnection(id: string): Promise<void> {
        await this.closeConnection(id);

        this.connectionConfigs = this.connectionConfigs.filter(conn => conn.id !== id);
        await this.context.globalState.update('connections', this.connectionConfigs);
    }

    public async testConnection(connection:  Omit<DatabaseConnectionConfig, 'id'>): Promise<boolean> {
 
        const id = Date.now().toString();
        const connectionConfig: DatabaseConnectionConfig = {
            id,
            ...connection
        };

        const provider = this.getProviderForConnectionConfig(connectionConfig);

        try {
            const client = await provider.createConnection(connectionConfig);
            await provider.closeConnection(client);
            console.info(`连接测试成功， ${connectionConfig.name} (${connectionConfig.type})`);
            return true;
        } catch (error) {
            console.error('连接测试失败，', error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }

    public async getDatabases(connectionConfigId: string): Promise<{ name: string }[]> {
        const client = await this.getOrCreateClient(connectionConfigId);
        const connectionConfig = this.getConnectionConfig(connectionConfigId);

        if (!connectionConfig) {
            throw new Error(`未能找到连接 ${connectionConfigId}`);
        }

        const provider = this.getProviderForConnectionConfig(connectionConfig);
        return provider.getDatabases(client);
    }

    public async getSchemas(connectionConfigId: string, database: string): Promise<{ name: string }[]> {
        const client = await this.getOrCreateClient(connectionConfigId);
        const connectionConfig = this.getConnectionConfig(connectionConfigId);

        if (!connectionConfig) {
            throw new Error(`Connection with ID ${connectionConfigId} not found`);
        }

        const provider = this.getProviderForConnectionConfig(connectionConfig);
        return provider.getSchemas(client, database);
    }

    public async getTables(
        connectionConfigId: string,
        database: string,
        schema?: string
    ): Promise<{ name: string, type: 'table' | 'view', comment?: string }[]> {
        const client = await this.getOrCreateClient(connectionConfigId);
        const connectionConfig = this.getConnectionConfig(connectionConfigId);

        if (!connectionConfig) {
            throw new Error(`Connection with ID ${connectionConfigId} not found`);
        }

        const provider = this.getProviderForConnectionConfig(connectionConfig);
        return provider.getTables(client, database, schema);
    }

    public async getColumns(
        connectionConfigId: string,
        database: string,
        table: string,
        schema?: string
    ): Promise<{
        name: string,
        type: string,
        isPrimaryKey: boolean,
        isForeignKey: boolean,
        references?: { table: string, column: string },
        comment?: string
    }[]> {
        const client = await this.getOrCreateClient(connectionConfigId);
        const connectionConfig = this.getConnectionConfig(connectionConfigId);

        if (!connectionConfig) {
            throw new Error(`Connection with ID ${connectionConfigId} not found`);
        }

        const provider = this.getProviderForConnectionConfig(connectionConfig);
        return provider.getColumns(client, database, table, schema);
    }

    public async executeQuery(
        connectionConfigId: string,
        query: string,
        database?: string,
        schema?: string
    ): Promise<{
        columns: string[],
        rows: any[],
        rowCount: number,
        executionTime: number
    }> {
        const client = await this.getOrCreateClient(connectionConfigId);
        const connectionConfig = this.getConnectionConfig(connectionConfigId);

        if (!connectionConfig) {
            throw new Error(`Connection with ID ${connectionConfigId} not found`);
        }

        const provider = this.getProviderForConnectionConfig(connectionConfig);

        const startTime = Date.now();
        const result = await provider.executeQuery(client, query, database, schema);
        const executionTime = Date.now() - startTime;

        return {
            ...result,
            executionTime
        };
    }


    private async getOrCreateClient(connectionConfigId: string): Promise<any> {
        if (this.activeConnections.has(connectionConfigId)) {
            return this.activeConnections.get(connectionConfigId);
        }

        const connectionConfig = this.getConnectionConfig(connectionConfigId);
        if (!connectionConfig) {
            throw new Error(`未发现连接 ${connectionConfigId}`);
        }

        const provider = this.getProviderForConnectionConfig(connectionConfig);

        try {
            console.debug(`创建连接 ${connectionConfig.name} (${connectionConfig.type})`);
            const client = await provider.createConnection(connectionConfig);

            this.activeConnections.set(connectionConfigId, client);
            console.info(`创建连接成功 ${connectionConfig.name} (${connectionConfig.type})`);

            return client;
        } catch (error) {
            console.error(`创建连接失败 ${connectionConfig.name} (${connectionConfig.type})`, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }

    private async closeConnection(connectionConfigId: string): Promise<void> {
        if (!this.activeConnections.has(connectionConfigId)) {
            return;
        }

        const client = this.activeConnections.get(connectionConfigId);
        const connectionConfig = this.getConnectionConfig(connectionConfigId);

        if (!connectionConfig) {
            throw new Error(`未发现连接 ${connectionConfigId}`);
        }

        const provider = this.getProviderForConnectionConfig(connectionConfig);

        try {
            console.debug(`开始关闭连接 ${connectionConfig.name} (${connectionConfig.type})`);
            await provider.closeConnection(client);
            console.info(`关闭连接成功 ${connectionConfig.name} (${connectionConfig.type})`);
        } catch (error) {
            console.error(`关闭连接失败 ${connectionConfig.name} (${connectionConfig.type})`, error instanceof Error ? error : new Error(String(error)));
            throw error;
        } finally {
            this.activeConnections.delete(connectionConfigId);
        }
    }

    private getProviderForConnectionConfig(connectionConfig: DatabaseConnectionConfig): ConnectionProvider {
        const provider = this.providers.get(connectionConfig.type);

        if (!provider) {
            throw new Error(`没有该类型的连接 provider : ${connectionConfig.type}`);
        }

        return provider;
    }

    async getDbStructure(connectionId: string, database: string) {
        const schemas = await this.getSchemas(connectionId, database);

        return {
            schemas: await Promise.all(
                schemas.map(async (schema) => {
                    const tables = await this.getTables(connectionId, database, schema.name);
                    return {
                        name: schema.name,
                        tables: await Promise.all(
                            tables.map(async (table) => {
                                const columns = await this.getColumns(
                                    connectionId,
                                    database,
                                    table.name,
                                    schema.name
                                );
                                return {
                                    name: table.name,
                                    type: table.type,
                                    columns: columns,
                                };
                            })
                        ),
                    };
                })
            ),
        };
    }
}   