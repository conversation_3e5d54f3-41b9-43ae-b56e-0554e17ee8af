import React from 'react';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
}

interface MessageListProps {
  messages: Message[];
  isLoading: boolean;
  messagesEndRef: React.RefObject<HTMLDivElement | null>;
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  });
};

const TypingIndicator = () => (
  <div className="flex items-center gap-2 p-3 vscode-bg-secondary border vscode-border rounded-lg self-start max-w-xs">
    <span className="text-sm vscode-text-primary">AI正在思考</span>
    <div className="flex gap-1">
      <div className="w-1.5 h-1.5 vscode-text-primary rounded-full animate-pulse" style={{ animationDelay: '0s', backgroundColor: 'var(--vscode-editor-foreground)' }} />
      <div className="w-1.5 h-1.5 vscode-text-primary rounded-full animate-pulse" style={{ animationDelay: '0.2s', backgroundColor: 'var(--vscode-editor-foreground)' }} />
      <div className="w-1.5 h-1.5 vscode-text-primary rounded-full animate-pulse" style={{ animationDelay: '0.4s', backgroundColor: 'var(--vscode-editor-foreground)' }} />
    </div>
  </div>
);

export const MessageList: React.FC<MessageListProps> = ({ messages, isLoading, messagesEndRef }) => {
  return (
    <div className="flex-1 overflow-y-auto py-4 flex flex-col gap-4 min-h-0">
      {messages.map((message) => (
        <div
          key={message.id}
          className={`p-3 rounded-lg max-w-4xl break-words ${
            message.role === 'user'
              ? 'self-end vscode-active'
              : 'self-start vscode-bg-secondary border vscode-border vscode-text-primary'
          }`}
        >
          <div className="text-sm leading-relaxed whitespace-pre-wrap">
            {message.content}
          </div>
          <div className="text-xs vscode-text-muted mt-1">
            {formatTime(message.timestamp)}
          </div>
        </div>
      ))}
      
      {isLoading && <TypingIndicator />}
      
      <div ref={messagesEndRef} />
    </div>
  );
};