import { BoxProps } from "@radix-ui/themes";

export type CodeEditorProps = Omit<BoxProps, 'resize' | 'onChange'> & {
  resize?: boolean;
  defaultValue?: string;
  value?: string;
  onChange?: (e: string) => void;
  onOpenModal?: () => void;
  variables?: EditorVariablePickerType[];
  defaultHeight?: number;
  language?: string;
};

type EditorVariablePickerType = {
  key: string;
  label: string;
};