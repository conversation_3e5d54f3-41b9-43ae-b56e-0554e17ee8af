import React from "react";
import { <PERSON>, <PERSON><PERSON>, Flex } from "@radix-ui/themes";
import { PlusIcon } from "@radix-ui/react-icons";
import { IOTitleProps } from "../interface";
import { useNodeInputsState, useNodeOutputsState } from "../../WorkflowCanvas";
import { createNewNodeInputField } from "../../WorkflowNodeInputs";
import { createNewNodeOutputField } from "../../WorkflowNodeOutputs";

const IOTitle: React.FC<IOTitleProps> = ({ text, nodeId, type, addable }) => {
  const { addInput } = useNodeInputsState(nodeId);
  const { addOutput } = useNodeOutputsState(nodeId);

  const handleAddInputOrOutputField = () => {
    if (type === "input") {
      // TODO Fix this
      addInput(createNewNodeInputField(nodeId));
    } else {
      // TODO Fix this
      addOutput(createNewNodeOutputField(nodeId));
    }
  };

  return (
    <Flex align="center" className="mb-2 font-md font-medium gap-1">
      <Flex flexGrow="1" align="center" gap="1">
        <div className="w-[3px] h-[14px] bg-blue-600 rounded-md"></div>
        <Box className="text-gray-900">{text}</Box>
      </Flex>
      {addable && <Button size="1" onClick={handleAddInputOrOutputField}>
        <PlusIcon className="w-4 h-4" />
        添加
      </Button>}
    </Flex>
  );
};

export default React.memo(IOTitle);
