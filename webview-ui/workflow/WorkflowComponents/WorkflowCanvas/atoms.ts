import { atom, useAtom } from "jotai";
import {
  applyNodeChanges,
  applyEdgeChanges,
  NodeChange,
  EdgeChange,
} from "@xyflow/react";
import {
  WorkflowNodeInputField,
  WorkflowNodeOuputField,
  WorkflowCondition,
  WorkflowEdge,
  ReactFlowNodeConfig,
} from "@lvjw/code-workflow-types";

export const nodesAtom = atom<ReactFlowNodeConfig[]>([
  {
      "id": "4a6af15439114cc5a0587fc523f2baf6",
      "type": "undefined",
      "position": {
          "x": 554,
          "y": -36
      },
      "data": {
          "showInputs": true,
          "showOutputs": true,
          "inputs": [
              {
                  "attrName": "config",
                  "label": "请求配置",
                  "removable": false,
                  "editable": false,
                  "value": {
                      "method": "POST",
                      "url": "/a/{b}",
                      "timeout": 30,
                      "params": [
                          {
                              "type": "const",
                              "key": "b",
                              "value": "1",
                              "valuePath": [],
                              "valueLabelPath": []
                          }
                      ],
                      "headers": [
                          {
                              "type": "const",
                              "key": "",
                              "value": "",
                              "valuePath": [],
                              "valueLabelPath": []
                          }
                      ],
                      "bodyType": "form-data",
                      "body": [
                          {
                              "type": "const",
                              "key": "aa",
                              "value": "1",
                              "valuePath": [],
                              "valueLabelPath": []
                          },
                          {
                              "type": "const",
                              "key": "bb",
                              "value": "2",
                              "valuePath": [],
                              "valueLabelPath": []
                          }
                      ]
                  },
                  "id": "5ac9e3812b7e4b31b6f9b90afdf07d85"
              }
          ],
          "outputs": [
              {
                  "attrName": "result",
                  "label": "结果",
                  "removable": true,
                  "editable": true,
                  "valueType": "string",
                  "isDynamic": true,
                  "valuePath": [],
                  "id": "3238f61b036d46cb97c226530e3b8b8c"
              }
          ],
          "label": "HTTP 请求",
          "hasCustomHandle": false
      },
      "measured": {
          "width": 662,
          "height": 1346
      },
      "selected": true,
      "dragging": false
  } as any,
]);
export const edgesAtom = atom<WorkflowEdge[]>([]);

export const onNodesChangeAtom = atom(
  null,
  (get, set, changes: NodeChange<ReactFlowNodeConfig>[]) => {
    const currentNodes = get(nodesAtom);
    const nextNodes = applyNodeChanges(changes, currentNodes);
    set(nodesAtom, nextNodes);
  }
);

export const onEdgesChangeAtom = atom(
  null, // Read part is not directly used, write part is key
  (get, set, changes: EdgeChange<WorkflowEdge>[]) => {
    const currentEdges = get(edgesAtom);
    const nextEdges = applyEdgeChanges(changes, currentEdges);
    set(edgesAtom, nextEdges);
  }
);

// 获取指定节点的 inputs
export const useNodeInputsState = (nodeId: string) => {
  const [nodes, setNodes] = useAtom(nodesAtom);

  const getInputs = () => {
    const node = nodes.find((n) => n.id === nodeId);
    return node?.data?.inputs || [];
  };

  const addInput = (input: WorkflowNodeInputField) => {
    setNodes((prevNodes) =>
      prevNodes.map((node) =>
        node.id === nodeId
          ? {
              ...node,
              data: {
                ...node.data,
                inputs: [...(node.data.inputs || []), input],
              },
            }
          : node
      )
    );
  };

  const deleteInput = (inputId: string) => {
    setNodes((prevNodes) =>
      prevNodes.map((node) =>
        node.id === nodeId
          ? {
              ...node,
              data: {
                ...node.data,
                inputs: (node.data.inputs || []).filter(
                  (input) => input.id !== inputId
                ),
              },
            }
          : node
      )
    );
  };

  const updateInput = (
    id: string,
    newInput: Partial<WorkflowNodeInputField>
  ) => {
    setNodes((prevNodes) =>
      prevNodes.map((node) =>
        node.id === nodeId
          ? {
              ...node,
              data: {
                ...node.data,
                inputs: (node.data.inputs || []).map((input) =>
                  input.id === id ? { ...input, ...newInput } : input
                ),
              },
            }
          : node
      )
    );
  };

  return {
    inputs: getInputs(),
    addInput,
    deleteInput,
    updateInput,
  };
};

// 获取指定节点的 outputs
export const useNodeOutputsState = (nodeId: string) => {
  const [nodes, setNodes] = useAtom(nodesAtom);

  const getOutputs = () => {
    const node = nodes.find((n) => n.id === nodeId);
    return node?.data?.outputs || [];
  };

  const addOutput = (output: WorkflowNodeOuputField) => {
    setNodes((prevNodes) =>
      prevNodes.map((node) =>
        node.id === nodeId
          ? {
              ...node,
              data: {
                ...node.data,
                outputs: [...(node.data.outputs || []), output],
              },
            }
          : node
      )
    );
  };

  const deleteOutput = (outputId: string) => {
    setNodes((prevNodes) =>
      prevNodes.map((node) =>
        node.id === nodeId
          ? {
              ...node,
              data: {
                ...node.data,
                outputs: (node.data.outputs || []).filter(
                  (output) => output.id !== outputId
                ),
              },
            }
          : node
      )
    );
  };

  const updateOutput = (
    id: string,
    newOutput: Partial<WorkflowNodeOuputField>
  ) => {
    setNodes((prevNodes) =>
      prevNodes.map((node) =>
        node.id === nodeId
          ? {
              ...node,
              data: {
                ...node.data,
                outputs: (node.data.outputs || []).map((output) =>
                  output.id === id ? { ...output, ...newOutput } : output
                ),
              },
            }
          : node
      )
    );
  };

  return {
    outputs: getOutputs(),
    addOutput,
    deleteOutput,
    updateOutput,
  };
};

// 便捷方法：更新节点 data 的属性
export function updateProperty(
  set: (fn: (prev: ReactFlowNodeConfig[]) => ReactFlowNodeConfig[]) => void,
  nodeId: string,
  property: string,
  value: any
): void {
  set((nodes) =>
    nodes.map((n) =>
      n.id === nodeId ? { ...n, data: { ...n.data, [property]: value } } : n
    )
  );
}

export const useNodeConditionState = (nodeId: string) => {
  const [nodes, setNodes] = useAtom(nodesAtom);

  const getConditionGroups = () => {
    const node = nodes.find((n) => n.id === nodeId);
    return node?.data?.conditions || [];
  };

  const updateConditionGroups = (conditionGroups: WorkflowCondition[]) => {
    setNodes((prevNodes) =>
      prevNodes.map((node) =>
        node.id === nodeId
          ? { ...node, data: { ...node.data, conditions: conditionGroups } }
          : node
      )
    );
  };

  return {
    conditionGroups: getConditionGroups(),
    updateConditionGroups,
  };
};
