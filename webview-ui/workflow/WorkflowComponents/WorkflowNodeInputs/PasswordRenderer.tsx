import React, { useState } from 'react';
import { TextField, IconButton } from '@radix-ui/themes';
import { EyeOpenIcon, EyeClosedIcon } from '@radix-ui/react-icons';

interface PasswordRendererProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
}

export const PasswordRenderer: React.FC<PasswordRendererProps> = ({
  value,
  onChange,
  placeholder = '请输入密码',
}) => {
  return (
    <div className="password-renderer">
         <TextField.Root placeholder={placeholder} type="password" value={value} onChange={(e) => onChange?.(e.target.value)}>
      </TextField.Root>
    </div>
  );
}; 