import { useState, useEffect } from "react"


interface Message {
    role: "user" | "bot";
    content: string;
}
// 简单生成唯一 sessionId
function createSessionId() {
    return (
      "session-" + Date.now() + "-" + Math.random().toString(36).slice(2, 10)
    );
  }

  const initialMessages: Message[] = [
    { role: "bot", content: "你好，请输入你的建表需求" },
  ];
  const createNewMessages = (): Message[] => (JSON.parse(JSON.stringify(initialMessages)));
  
  const vscode =
  (window as any).vscode ||
  ((window as any).acquireVsCodeApi && (window as any).acquireVsCodeApi());
if (vscode) (window as any).vscode = vscode;
export const useAnalyzeRequirement = () => {
    const [sessionId, setSessionId] = useState<string>(createSessionId());
    const [messages, setMessages] = useState<Message[]>(createNewMessages());
    const [currentSessionIdx, setCurrentSessionIdx] = useState(0);
    const [input, setInput] = useState("");
    const [isClarifying, setIsClarifying] = useState(false);
    const [loading, setLoading] = useState(false);
    const [isStreaming, setIsStreaming] = useState(false); // To track streaming state

    useEffect(() => {
        function handleVSCodeMessage(event: MessageEvent) {
          const { type, sessionId: receivedSessionId, content, questions, message, payload } = event.data || {};
    
          if (receivedSessionId !== sessionId) return; // Ignore messages from other sessions

          if (type === 'llmChunk' && content) {
            setMessages(prev => {
              const lastMessage = prev[prev.length - 1];
              if (lastMessage && lastMessage.role === 'bot' && isStreaming) {
                // Append to the last bot message if streaming is active
                return [
                  ...prev.slice(0, -1),
                  { ...lastMessage, content: lastMessage.content + content },
                ];
              } else {
                // Start a new bot message for the stream
                setIsStreaming(true);
                return [...prev, { role: 'bot', content }];
              }
            });
            setLoading(true); // Keep loading during stream
          } else if (type === 'requirementAnalysisStreamEnd') {
            setIsStreaming(false);
            setLoading(false); // Stop loading when stream ends
          } else if (type === 'createTableChatLLMMessage' && content) { // This might be deprecated if all LLM responses are streamed
            setMessages(prev => {
              return [...prev, { role: 'bot', content }];
            });
            setLoading(false);
          } else if (
            type === "askClarificationQuestionsInWebview" &&
            Array.isArray(questions)
          ) {
            setMessages(prev => {
                return [...prev, { role: 'bot', content: "请回答以下问题：\n" + questions.map((item: string, index) => {
                    return `${index + 1}. ${item}`;
                }).join("\n")  }];
              })
            setIsClarifying(true);
            setLoading(false);
          } else if (type === 'requirementClear' && content) {
            setMessages(prev => {
              return [...prev, { role: 'bot', content }];
            });
            setLoading(false);
          } else if (type === 'requirementAnalysisResult' && payload) {
            // This message might be used to confirm the final state or update UI elements
            // not directly handled by the stream (e.g., enabling/disabling buttons).
            // For now, we'll just log it, as the content is built by the stream.
            console.log('Final analysis result received:', payload);
            // If you need to update messages based on the final payload, you can do it here.
            // For example, if the stream only sends raw text and this payload contains structured data.
            setLoading(false); // Ensure loading is false
          }
        }
        window.addEventListener("message", handleVSCodeMessage);
        return () => window.removeEventListener("message", handleVSCodeMessage);
      }, [sessionId, isStreaming]); // Added sessionId and isStreaming to dependency array

  
    return {
        sessionId,
        messages,
        loading,
        onInputChange: (value:string) => {
            setInput(value);
        },
        onCreate: () => {
            setSessionId(createSessionId());
            setMessages(createNewMessages());
            setCurrentSessionIdx(0);
            setInput("");
        },
        onSend: () => {
            if (!input) return;
            setLoading(true)
            // When user sends a message, if a stream was active, it should have been finalized by 'requirementAnalysisStreamEnd'.
            // Reset streaming state for the new user message.
            setIsStreaming(false); 
            setMessages([...messages, { role: "user", content: input }]);
            if (vscode) {
                if (isClarifying) {
                    vscode.postMessage({
                        type: 'clarificationAnswersFromWebview',
                        sessionId,
                        data: input,
                      });
                    setIsClarifying(false);
                } else {
                    vscode.postMessage({
                        type: "createTableChatUserMessage",
                        sessionId,
                        content: input,
                    });
                }
                setInput("");
            }
        }
    }
}