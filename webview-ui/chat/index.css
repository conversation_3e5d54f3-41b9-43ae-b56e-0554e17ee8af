@import "tailwindcss";
@import "@radix-ui/themes/styles.css";

/* VS Code 主题变量 */
:root {
  /* 默认深色主题颜色 */
  --vscode-editor-background: #1e1e1e;
  --vscode-editor-foreground: #d4d4d4;
  --vscode-sideBar-background: #252526;
  --vscode-sideBar-foreground: #cccccc;
  --vscode-sideBar-border: #2b2b2b;
  --vscode-input-background: #3c3c3c;
  --vscode-input-foreground: #cccccc;
  --vscode-input-border: #3c3c3c;
  --vscode-input-placeholderForeground: #a6a6a6;
  --vscode-button-background: #0e639c;
  --vscode-button-foreground: #ffffff;
  --vscode-button-hoverBackground: #1177bb;
  --vscode-list-hoverBackground: #2a2d2e;
  --vscode-list-activeSelectionBackground: #094771;
  --vscode-list-activeSelectionForeground: #ffffff;
  --vscode-badge-background: #4d4d4d;
  --vscode-badge-foreground: #ffffff;
  --vscode-progressBar-background: #0e639c;
  --vscode-panel-border: #2b2b2b;
  --vscode-textLink-foreground: #3794ff;
  --vscode-descriptionForeground: #9d9d9d;
}

/* 浅色主题适配 */
[data-vscode-theme-kind="vscode-light"] {
  --vscode-editor-background: #ffffff;
  --vscode-editor-foreground: #333333;
  --vscode-sideBar-background: #f3f3f3;
  --vscode-sideBar-foreground: #333333;
  --vscode-sideBar-border: #e7e7e7;
  --vscode-input-background: #ffffff;
  --vscode-input-foreground: #333333;
  --vscode-input-border: #cecece;
  --vscode-input-placeholderForeground: #767676;
  --vscode-button-background: #007acc;
  --vscode-button-foreground: #ffffff;
  --vscode-button-hoverBackground: #005a9e;
  --vscode-list-hoverBackground: #f0f0f0;
  --vscode-list-activeSelectionBackground: #0060c0;
  --vscode-list-activeSelectionForeground: #ffffff;
  --vscode-badge-background: #c4c4c4;
  --vscode-badge-foreground: #333333;
  --vscode-progressBar-background: #007acc;
  --vscode-panel-border: #e7e7e7;
  --vscode-textLink-foreground: #006ab1;
  --vscode-descriptionForeground: #717171;
}

/* 高对比度主题适配 */
[data-vscode-theme-kind="vscode-high-contrast"] {
  --vscode-editor-background: #000000;
  --vscode-editor-foreground: #ffffff;
  --vscode-sideBar-background: #000000;
  --vscode-sideBar-foreground: #ffffff;
  --vscode-sideBar-border: #6fc3df;
  --vscode-input-background: #000000;
  --vscode-input-foreground: #ffffff;
  --vscode-input-border: #6fc3df;
  --vscode-input-placeholderForeground: #ffffff;
  --vscode-button-background: #0e639c;
  --vscode-button-foreground: #ffffff;
  --vscode-button-hoverBackground: #1177bb;
  --vscode-list-hoverBackground: #2a2d2e;
  --vscode-list-activeSelectionBackground: #094771;
  --vscode-list-activeSelectionForeground: #ffffff;
  --vscode-badge-background: #000000;
  --vscode-badge-foreground: #ffffff;
  --vscode-progressBar-background: #0e639c;
  --vscode-panel-border: #6fc3df;
  --vscode-textLink-foreground: #3794ff;
  --vscode-descriptionForeground: #ffffff;
}

html, body {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  background-color: var(--vscode-editor-background);
  color: var(--vscode-editor-foreground);
}

/* 自定义CSS类用于组件 */
.vscode-bg-primary {
  background-color: var(--vscode-editor-background);
}

.vscode-bg-secondary {
  background-color: var(--vscode-sideBar-background);
}

.vscode-text-primary {
  color: var(--vscode-editor-foreground);
}

.vscode-text-secondary {
  color: var(--vscode-sideBar-foreground);
}

.vscode-text-muted {
  color: var(--vscode-descriptionForeground);
}

.vscode-border {
  border-color: var(--vscode-panel-border);
}

.vscode-input {
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  border-color: var(--vscode-input-border);
}

.vscode-input::placeholder {
  color: var(--vscode-input-placeholderForeground);
}

.vscode-button {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.vscode-button:hover {
  background-color: var(--vscode-button-hoverBackground);
}

.vscode-hover {
  background-color: var(--vscode-list-hoverBackground);
}

.vscode-active {
  background-color: var(--vscode-list-activeSelectionBackground);
  color: var(--vscode-list-activeSelectionForeground);
}