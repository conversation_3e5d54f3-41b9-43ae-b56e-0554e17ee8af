import * as vscode from "vscode";
import * as path from "path";

export class WorkflowTreeDataProvider
  implements vscode.TreeDataProvider<vscode.TreeItem>
{
  private _onDidChangeTreeData: vscode.EventEmitter<vscode.TreeItem | undefined | void> = new vscode.EventEmitter();
  readonly onDidChangeTreeData: vscode.Event<vscode.TreeItem | undefined | void> = this._onDidChangeTreeData.event;

  getTreeItem(element: vscode.TreeItem): vscode.TreeItem {
    return element;
  }

  async getChildren(element?: vscode.TreeItem): Promise<vscode.TreeItem[]> {
    const files = await vscode.workspace.findFiles("**/*.flow.json");
    return files.map((uri) => {
      // 只显示文件名（去掉 .flow.json 后缀）
      const basename = path.basename(uri.fsPath, ".flow.json");
      const item = new vscode.TreeItem(
        basename,
        vscode.TreeItemCollapsibleState.None
      );
      item.resourceUri = uri;
      item.iconPath = new vscode.ThemeIcon("circuit-board");
      item.command = {
        command: "dev-expert.openWorkflowWebview",
        title: "查看",
        arguments: [basename, uri],
      };
      return item;
    });
  }

  // 新增刷新方法
  refresh(): void {
    this._onDidChangeTreeData.fire();
  }
}
