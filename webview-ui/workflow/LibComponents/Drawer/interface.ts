/**
 * 定义 Drawer 组件可接受的 'side' 方向的类型
 */
export type DrawerSide = 'top' | 'bottom' | 'left' | 'right';

/**
 * Drawer 组件的 Props 接口
 */
export interface DrawerProps {
  /**
   * 控制抽屉的打开和关闭状态。
   * @default false
   */
  open?: boolean;

  /**
   * 当抽屉的打开状态发生变化时调用的回调函数。
   * Radix Dialog 在用户尝试关闭时（例如按 Esc 键或点击遮罩层）会调用此函数。
   */
  onOpenChange?: (open: boolean) => void;

  /**
   * 抽屉的可访问标题。
   * 将使用 `Dialog.Title` 渲染。
   */
  title: string; // 标题通常是必需的，为了可访问性

  /**
   * 抽屉的可访问描述 (可选)。
   * 将使用 `Dialog.Description` 渲染。
   */
  description?: string;

  /**
   * 触发打开抽屉的元素 (可选)。
   * 如果不提供，你需要从父组件完全控制 `open` 状态。
   * `Dialog.Trigger` 会自动处理点击事件。
   */
  trigger?: React.ReactNode;

  /**
   * 抽屉滑出的方向。
   * @default 'right'
   */
  side?: DrawerSide;

  /**
   * 抽屉的主要内容。
   */
  children: React.ReactNode;

  /**
   * 允许传递额外的 CSS 类名到 Drawer 的 Dialog.Content 元素。
   * 这些类名会通过 `tailwind-merge` 与组件内部的类名合并。
   */
  className?: string;
}