import { Node } from 'pocketflow'
import { replaceFile } from '../utils/replaceFile'
import {
    CodingAgentSharedStore,
    EditOperation,
    EditCodeAgentActionEnum,
    History,
    Output,
} from '../types'
import path from 'path'

interface PrepResult {
    sortedEdits: EditOperation[]
    targetFile: string
    history: History[]
    output: Output
}

export class ApplyChangesBatchNode extends Node<CodingAgentSharedStore> {
    async prep(shared: CodingAgentSharedStore): Promise<PrepResult> {
        const targetFile = path.resolve(shared.workingDir, shared.currentAction!.params.targetFile)

        // 按 startLine 降序排序，从文件底部开始编辑
        const sortedEdits = [...shared.editOperations].sort((a, b) => b.startLine - a.startLine)

        return { sortedEdits, targetFile, history: shared.history, output: shared.output }
    }

    async exec(
        prepResult: PrepResult,
    ): Promise<{ results: Array<{ success: boolean; message: string }> }> {


        const results = []

        for (const edit of prepResult.sortedEdits) {
          
            prepResult.output.text(`正在应用编辑 ${edit.startLine}-${edit.endLine}...`)

            const [message, success] = await replaceFile(
                prepResult.targetFile,
                edit.startLine,
                edit.endLine,
                edit.replacement,
            )

            results.push({ success, message })

            if (!success) {
                prepResult.output.text(`编辑失败: ${message}`)
                break
            }
        }

        return { results }
    }

    async post(
        shared: CodingAgentSharedStore,
        prepResult: PrepResult,
        execResult: { results: Array<{ success: boolean; message: string }> },
    ) {
        prepResult.output.text('更新编辑结果') 

        shared.currentAction!.result = execResult.results
        shared.currentAction!.success = execResult.results.every(r => r.success)

        shared.history.push({
          role: 'user',
          content: shared.currentAction!
        })

        shared.currentAction = null

        // 清空编辑操作
        shared.editOperations = []

        return EditCodeAgentActionEnum.default
    }
}
