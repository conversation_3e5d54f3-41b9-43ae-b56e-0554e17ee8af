import { removeFile } from './removeFile'
import { insertFile } from './insertFile'
import path from 'path'
import { Logger } from './logger'

export async function replaceFile(
  targetFile: string,
  startLine: number,
  endLine: number,
  content: string,
  output?: (response: string) => void,
  logger?: Logger
): Promise<[string, boolean]> {
  try {
    // Check if file exists
    const fs = await import('fs/promises')
    try {
      await fs.access(targetFile)
    } catch {
      // TODO: 递归创建目录和文件
      await fs.mkdir(path.dirname(targetFile), { recursive: true })
      await fs.writeFile(targetFile, '', 'utf-8')
    }

    // Validate line numbers
    if (startLine < 1) {
      return ['Error: startLine must be at least 1', false]
    }

    if (endLine < 1) {
      return ['Error: endLine must be at least 1', false]
    }

    if (startLine > endLine) {
      return ['Error: startLine must be less than or equal to endLine', false]
    }

    // First, remove the specified lines
    const [removeResult, removeSuccess] = await removeFile(targetFile, startLine, endLine)

    if (!removeSuccess) {
      return [`Error during remove step: ${removeResult}`, false]
    }

    // Then, insert the new content at the start line
    const [insertResult, insertSuccess] = await insertFile(targetFile, content, startLine)

    if (!insertSuccess) {
      return [`Error during insert step: ${insertResult}`, false]
    }

    return [`Successfully replaced lines ${startLine} to ${endLine} in ${targetFile}`, true]
  } catch (error) {
    return [`Error replacing content: ${error}`, false]
  }
}
