import fs from "fs/promises";
import path from "path";
import { glob } from "glob";

export interface SearchResult {
  file: string;
  line_number: number;
  content: string;
}

function globToRegex(pattern: string, output?: (response: string) => void,): RegExp[] {
  // Simple glob to regex conversion
  const regexPattern = pattern
    .replace(/\./g, "\\.")
    .replace(/\*/g, ".*")
    .replace(/\?/g, ".");
  return [new RegExp(`^${regexPattern}$`)];
}

export async function grepSearch(
  query: string,
  caseSensitive: boolean = true,
  includePattern?: string,
  excludePattern?: string,
  workingDir: string = ""
): Promise<[boolean, SearchResult[] ]> {
  const results: SearchResult[] = [];
  const searchDir = workingDir || process.cwd();

  try {
    // Compile the regex pattern
    let pattern: RegExp;
    try {
      pattern = new RegExp(query, caseSensitive ? "" : "i");
    } catch (error) {
      console.log(`Invalid regex pattern: ${error}`);
      return [false, [] ];
    }

    // Convert glob patterns to regex for file matching
    const includeRegexes = includePattern ? globToRegex(includePattern) : null;
    const excludeRegexes = excludePattern ? globToRegex(excludePattern) : null;

    // Get all files recursively
    const files = await glob("**/*", {
      cwd: searchDir,
      nodir: true,
      absolute: false,
      // 排除 node_modules
      ignore: ["**/node_modules/**"],
    });

    let matchCount = 0;
    const maxMatches = 50;

    for (const file of files) {
      if (matchCount >= maxMatches) break;

      const filename = path.basename(file);

      // Skip files that don't match inclusion pattern
      if (includeRegexes && !includeRegexes.some((r) => r.test(filename))) {
        continue;
      }

      // Skip files that match exclusion pattern - use full file path for exclusion
      if (excludeRegexes && excludeRegexes.some((r) => r.test(file))) {
        continue;
      }

      try {
        const filePath = path.join(searchDir, file);
        const content = await fs.readFile(filePath, "utf-8");
        const lines = content.split("\n");

        for (let lineNum = 0; lineNum < lines.length; lineNum++) {
          const line = lines[lineNum];
          if (pattern.test(line)) {
            results.push({
              file: file,
              line_number: lineNum + 1,
              content: line,
            });
          }
        }
      } catch (error) {
        // Skip files that can't be read (binary files, permission issues, etc.)
        continue;
      } finally {
        matchCount++;
      }
    }

    return [true, results ];
  } catch (error) {
    console.log(`Search error: ${error}`);
    return [false, [] ];
  }
}
