export type MessageFromWebview =
    | GetContentMessage
    | GetWorkflowPathMessage
    | SaveWorkflowMessage
    | QueryLLMAboutPgSql
    | SendAIMessage;

export interface GetContentMessage {
    type: "getContent";
}

export interface GetWorkflowPathMessage {
    type: "getWorkflowPath";
    content: string;
}

export interface SaveWorkflowMessage {
    type: "save";
    content: any;
}

export interface QueryLLMAboutPgSql {
    type: "queryLLMAboutPgSql";
    query: string;
}

export interface SendAIMessage {
    type: "sendMessage";
    content: string;
}

export type MessageToWebview = updateWorkflowMessage;

export interface updateWorkflowMessage {
    type: "getContent";
    // TODO graph
    content: any;
}
