import React from 'react';
import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import {
  isTableEditDrawerOpenAtom,
  editingTableIdAtom,
  erNodesAtom,
  updateTableAtom,
} from './atoms';
import { Dialog, Button, Flex, Text, TextField, TextArea, Separator, Box, IconButton } from '@radix-ui/themes';
import { PlusIcon, Cross1Icon, Pencil1Icon } from '@radix-ui/react-icons'; // Added Pencil1Icon
import { useEffect, useState } from 'react';
import type { Column } from './interface';
import { ColumnEditDialog } from './ColumnEditDialog'; // Import ColumnEditDialog

export function TableEditDrawer() {
  const [isOpen, setIsOpen] = useAtom(isTableEditDrawerOpenAtom);
  const editingTableId = useAtomValue(editingTableIdAtom);
  const tables = useAtomValue(erNodesAtom);
  const setUpdateTable = useSetAtom(updateTableAtom);

  const table = tables.find(t => t.id === editingTableId);

  const [tableName, setTableName] = useState('');
  const [tableComment, setTableComment] = useState('');
  const [columns, setColumns] = useState<Column[]>([]);

  // State for ColumnEditDialog
  const [isColumnDialogOpen, setIsColumnDialogOpen] = useState(false);
  const [editingColumn, setEditingColumn] = useState<Column | null>(null);
  const [editingColumnIndex, setEditingColumnIndex] = useState<number | null>(null);

  useEffect(() => {
    if (table) {
      setTableName(table.data.name as string || '');
      setTableComment(table.data.comment as string || '');
      setColumns(table.data.columns ? JSON.parse(JSON.stringify(table.data.columns)) : []);
    } else {
      setTableName('');
      setTableComment('');
      setColumns([]);
    }
    // Reset column dialog states when main dialog opens or table changes
    setIsColumnDialogOpen(false);
    setEditingColumn(null);
    setEditingColumnIndex(null);
  }, [table, isOpen]);

  const handleOpenAddColumnDialog = () => {
    setEditingColumn(null); // For new column
    setEditingColumnIndex(null);
    setIsColumnDialogOpen(true);
  };

  const handleOpenEditColumnDialog = (column: Column, index: number) => {
    setEditingColumn(JSON.parse(JSON.stringify(column))); // Deep copy to avoid direct state mutation issues
    setEditingColumnIndex(index);
    setIsColumnDialogOpen(true);
  };

  const handleSaveColumn = (savedColumn: Column) => {
    const newColumns = [...columns];
    if (editingColumnIndex !== null) {
      // Editing existing column
      newColumns[editingColumnIndex] = savedColumn;
    } else {
      // Adding new column
      newColumns.push(savedColumn);
    }
    setColumns(newColumns);
    setIsColumnDialogOpen(false);
    setEditingColumn(null);
    setEditingColumnIndex(null);
  };

  const handleRemoveColumn = (indexToRemove: number) => {
    setColumns(columns.filter((_, index) => index !== indexToRemove));
  };

  const handleSave = () => {
    if (editingTableId) {
      setUpdateTable({
        id: editingTableId,
        name: tableName,
        comment: tableComment,
        columns: columns,
      });
    }
    setIsOpen(false);
  };

  const handleCancel = () => {
    setIsOpen(false);
  };

  if (!table) {
    return null;
  }

  return (
    <>
      <Dialog.Root open={isOpen} onOpenChange={setIsOpen}>
        <Dialog.Content style={{ maxWidth: 550 }}>
          <Dialog.Title>编辑表: {table.data.name as string}</Dialog.Title>
          <Dialog.Description size="2" mb="4">
            修改表的详细信息。
          </Dialog.Description>

          <Flex direction="column" gap="3">
            <label>
              <Text as="div" size="2" mb="1" weight="bold">
                表名
              </Text>
              <TextField.Root
                value={tableName}
                onChange={(e) => setTableName(e.target.value)}
                placeholder="输入表名"
              />
            </label>
            <label>
              <Text as="div" size="2" mb="1" weight="bold">
                注释
              </Text>
              <TextArea
                value={tableComment}
                onChange={(e) => setTableComment(e.target.value)}
                placeholder="输入表注释"
              />
            </label>
          </Flex>

          <Separator my="3" size="4" />

          <Flex justify="between" align="center" mb="2">
            <Text as="div" size="2" weight="bold">
              列信息
            </Text>
            <Button size="1" onClick={handleOpenAddColumnDialog} variant="soft">
              <PlusIcon width="12" height="12" /> 添加列
            </Button>
          </Flex>
          <Box style={{ maxHeight: '300px', overflowY: 'auto', paddingRight: '10px' }}>
            <Flex direction="column" gap="2"> {/* Reduced gap for list items */}
              {columns.map((column, index) => (
                <Box key={index} p="2" style={{ border: '1px solid var(--gray-a7)', borderRadius: 'var(--radius-3)' }}>
                  <Flex justify="between" align="center">
                    <Flex direction="column">
                      <Text size="2" weight="medium">{column.name}</Text>
                      {column.comment && <Text size="1" color="gray">{column.comment}</Text>}
                    </Flex>
                    <Flex gap="2">
                      <IconButton size="1" color="gray" variant="ghost" onClick={() => handleOpenEditColumnDialog(column, index)} aria-label="编辑列">
                        <Pencil1Icon width="12" height="12" />
                      </IconButton>
                      <IconButton size="1" color="red" variant="ghost" onClick={() => handleRemoveColumn(index)} aria-label="删除列">
                        <Cross1Icon width="12" height="12" />
                      </IconButton>
                    </Flex>
                  </Flex>
                </Box>
              ))}
              {columns.length === 0 && (
                <Text size="2" color="gray" align="center" className="py-4">
                  当前没有定义列信息，点击 "添加列" 开始创建。
                </Text>
              )}
            </Flex>
          </Box>

          <Flex gap="3" mt="4" justify="end">
            <Button variant="soft" color="gray" onClick={handleCancel}>
              取消
            </Button>
            <Button onClick={handleSave}>保存</Button>
          </Flex>
        </Dialog.Content>
      </Dialog.Root>

      <ColumnEditDialog
        isOpen={isColumnDialogOpen}
        onOpenChange={setIsColumnDialogOpen}
        columnData={editingColumn}
        onSave={handleSaveColumn}
      />
    </>
  );
}