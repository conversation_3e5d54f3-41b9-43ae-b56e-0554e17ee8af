import React, { useEffect, useState } from "react";
import { Button, Text, Flex } from "@radix-ui/themes";
import { useAtom } from "jotai";
import { edgesAtom, nodesAtom } from "../WorkflowCanvas";
import CodeEditor from "../../LibComponents/CodeEditor";

export const WorkflowRunDrawerContent: React.FC<{ workflowPath: string }> = ({
  workflowPath,
}: {
  workflowPath: string;
}) => {
  const [responseText, setResponseText] = useState<string>("");
  const [nodes, setNodes] = useAtom(nodesAtom);
  const [edges, setEdges] = useAtom(edgesAtom);
  const [editorValue, setEditorValue] = useState<string>(`{
  "a": 2,
  "b": 2
}`);
  const [loading, setLoading] = useState(false);
  const [dialogHistory, setDialogHistory] = useState<
    { type: "input" | "output"; content: string }[]
  >([]);

  const handleRunWorkflow = async () => {
    try {
      setLoading(true);
      let body;
      try {
        body = JSON.parse(editorValue);
      } catch (e) {
        alert("请输入合法的 JSON");
        return;
      }
      setLoading(true);
      setResponseText("");
      const requestPath = workflowPath
        .replace("src/flows/", "")
        .replace(".flow.json", "");

      const inputStr = JSON.stringify(body, null, 2);
      let outputStr = "";
      setDialogHistory((history) => [
        ...history,
        { type: "input", content: inputStr },
      ]);

      const response = await fetch(
        `http://localhost:3058/api/dev/${requestPath}`,
        {
          method: "POST",
          body: JSON.stringify(body),
          headers: {
            "Content-Type": "application/json",
          }
        }
      );

      const reader = response.body
        ?.pipeThrough(new TextDecoderStream())
        .getReader();
      if (!reader) {
        setLoading(false);
        throw new Error("无法创建流读取器");
      }
      // 用于存储完整消息
      let buffer = ""; // 用于存储部分消息

      while (true) {
        const { value, done } = await reader.read();

        if (done) {
          console.log("Stream complete");
          break;
        }

        buffer += value;
        // console.log('Raw chunk:', value); // 原始数据块
        // console.log('Current buffer:', buffer);

        let boundary = buffer.indexOf("\n\n");
        while (boundary !== -1) {
          const message = buffer.substring(0, boundary);
          buffer = buffer.substring(boundary + 2); // 移除已处理的消息和两个换行符

          // 解析 SSE 消息
          const lines = message.split("\n");

          for (const line of lines) {
            try {
              const data = JSON.parse(line.trim());
              switch (data.type) {
                case "connected":
                  setDialogHistory((history) => [
                    ...history,
                    { type: "output", content: "连接成功" },
                  ]);
                  break;
                // case "nodeStart":
                //   setDialogHistory(history => [...history, { input: '', output: `${data.nodeName}节点开始执行` }]);
                //   break;
                case "nodeSuccess":
                  setDialogHistory((history) => [
                    ...history,
                    { type: "output", content: `${data.nodeName}节点执行成功` },
                  ]);
                  break;
                case "nodeError":
                  setDialogHistory((history) => [
                    ...history,
                    {
                      type: "output",
                      content: `${data.nodeName}节点执行失败: ${data.error}`,
                    },
                  ]);
                  break;
                case "workflowComplete":
                  console.log("workflowComplete", data);
                  setDialogHistory((history) => [
                    ...history,
                    { type: "output", content: "工作流执行完成" },
                  ]);
                  setDialogHistory((history) => [
                    ...history,
                    {
                      type: "output",
                      content:
                        JSON.stringify(data?.outputs ?? {}, null, 2),
                    },
                  ]);
                  break;
                case "workflowError":
                  setDialogHistory((history) => [
                    ...history,
                    { type: "output", content: "工作流执行出错:" + data.error },
                  ]);
                  break;
                default:
                  // 对于其他类型的消息，如果需要显示，也可以将其作为 output 处理
                  // outputStr += line + "\n";
                  break;
              }
            } catch (error) {
              // 忽略解析失败
            }
          }
          boundary = buffer.indexOf("\n\n");
        }
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Flex direction="column" style={{ height: "100%" }}>
      {/* 对话内容区 */}
      <Flex
        style={{
          flex: 1,
          overflow: "auto",
          flexDirection: "column",
          gap: 16,
          padding: 8,
        }}
      >
        {dialogHistory.map((item, idx) => (
          <Flex
            key={idx}
            direction="column"
            align={item.type === "input" ? "start" : "end"}
            gap="4"
            style={{ width: "100%" }}
          >
            <div style={{ color: "#888", fontSize: 12, marginBottom: 2 }}>
              {item.type === "input" ? "输入" : "输出"}
            </div>
            <pre
              style={{
                maxWidth: "80%",
                background: item.type === "input" ? "#f3f4f6" : "#e0f2fe",
                borderRadius: 6,
                padding: 8,
                whiteSpace: "pre-wrap",
                wordBreak: "break-all",
                fontSize: 14,
                textAlign: 'left'
              }}
            >
              {item.content}
            </pre>
          </Flex>
        ))}
      </Flex>
      {/* 底部输入区 */}
      <div
        style={{
          width: "100%",
          borderTop: "1px solid #eee",
          padding: 8,
          background: "#fff",
        }}
      >
        <CodeEditor
          value={editorValue}
          onChange={setEditorValue}
          language="json"
          defaultHeight={200}
        />
        <Button
          onClick={handleRunWorkflow}
          className="mt-2! w-full!"
          variant="solid"
          color="blue"
          disabled={loading}
        >
          {loading ? "处理中..." : "发送"}
        </Button>
      </div>
    </Flex>
  );
};
