import React from "react";
import {
  WorkflowNodeInputFieldProps,
} from "./interface";
import { Box, Flex, TextField, Tooltip, IconButton } from "@radix-ui/themes";
import { QuestionMarkCircledIcon, TrashIcon } from "@radix-ui/react-icons";
import { WorkflowInputFieldDialog } from "./WorkflowInputFieldDialog";
import { useNodeInputsState } from "../WorkflowCanvas";
import { WorkflowNodeInputFieldRenderer, WorkflowNodeInputField } from "./WorkflowNodeInputFieldRenderer";

export const WorkflowNodeInputFieldComp: React.FC<
  WorkflowNodeInputFieldProps
> = (props) => {
  const { attrName, label, desc, isDynamic, value, valueType } = props.input;

  const { updateInput, deleteInput } = useNodeInputsState(props.node.id);

  const handleUpdateInputField = async (
    form: Partial<WorkflowNodeInputField>
  ) => {
    updateInput(props.input.id, form);
  };

  const handleDeleteInputField = async () => {
    deleteInput(props.input.id);
  };

  return (
    <>
      <Box position="relative">
        <Flex justify="between" className="nodrag cursor-default">
          <Flex align="center" gap="1" className="mb-1.5">
            <Box className="font-gray-600">{label || attrName}</Box>
            {desc && (
              <Tooltip content={desc}>
                <IconButton variant="ghost" radius="full">
                  <QuestionMarkCircledIcon color="#666" />
                </IconButton>
              </Tooltip>
            )}
            {/* TODO Tag 组件 */}
            <Box style={{lineHeight: '16px'}} className="text-gray-600 bg-gray-200 px-1 h-4 rounded-sm text-xs">
              {isDynamic ? "动态" : "静态"}
            </Box>
            <Box style={{lineHeight: '16px'}} className="text-gray-600 bg-gray-200 px-1 h-4 rounded-sm text-xs">
              {valueType}
            </Box>
          </Flex>
          <Flex align="center" gap="2">
            {/* 设置按钮 */}
            <WorkflowInputFieldDialog
              node={props.node}
              input={props.input}
              onSave={handleUpdateInputField}
            />

            {/* 删除按钮 */}
            <IconButton
              disabled={!props.input.removable}
              className="cursor-pointer"
              color={props.input.removable ? "red" : "gray"}
              variant="ghost"
              radius="full"
              onClick={() => deleteInput(props.input.id)}
            >
              <TrashIcon />
            </IconButton>
          </Flex>
        </Flex>
        {/* 输入框 */}
        <WorkflowNodeInputFieldRenderer
          input={props.input}
          node={props.node}
        />
      </Box>
    </>
  );
};
