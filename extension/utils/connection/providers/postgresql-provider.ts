import { ConnectionProvider } from "../interface";
import { DatabaseConnectionConfig } from "../interface";
import pg, { PoolClient, Connection, Pool } from 'pg';


export class PostgreSQLProvider implements ConnectionProvider {
    pg: any;

    constructor() {
        try {
            this.pg = pg;
        } catch (error) {
            console.error('加载 pg npm 包失败', error instanceof Error ? error : new Error(String(error)));
        }
    }

    async createConnection(connection: DatabaseConnectionConfig): Promise<Pool> {
        try {
            console.debug(`正在创建 pg 连接，${connection.host}:${connection.port}`);

            if (!this.pg) {
                throw new Error('加载 pg npm 包失败. 请安装 pg npm 包.');
            }

            const client = new this.pg.Client({
                host: connection.host,
                port: connection.port,
                user: connection.username,
                password: connection.password,
                database: connection.database || 'postgres', // Default to postgres database
                ssl: connection.options?.ssl,
                connectionTimeoutMillis: 10000 // 10 seconds
            });

            await client.connect();
            console.info(`连接 pg 数据库成功， ${connection.host}:${connection.port}`);

            return client;
        } catch (error) {
            console.error('连接 pg 数据库失败', error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }

    async closeConnection(client: Connection): Promise<void> {
        try {
            await client.end();
            console.debug('pg 连接已成功断开');
        } catch (error) {
            console.error('断开 pg 连接失败，', error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }

    async getDatabases(client: Pool): Promise<{ name: string }[]> {
        try {
            // Query to get all databases
            const result = await client.query(`
                SELECT datname as name
                FROM pg_database
                WHERE datistemplate = false
                ORDER BY datname
            `);

            return result.rows;
        } catch (error) {
            console.error('获取 pg 数据库信息失败，', error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }

    async getSchemas(client: Pool, _database: string): Promise<{ name: string }[]> {
        try {
            // Query to get all schemas
            const result = await client.query(`
                SELECT schema_name as name
                FROM information_schema.schemata
                WHERE schema_name NOT LIKE 'pg_%'
                  AND schema_name != 'information_schema'
                ORDER BY schema_name
            `);

            return result.rows;
        } catch (error) {
            console.error('获取 pg schemas 失败，', error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }

    async getTables(
        client: Pool,
        _database: string,
        schema: string = 'public'
    ): Promise<{ name: string, type: 'table' | 'view', comment?: string }[]> {
        try {
            const result = await client.query(`
                SELECT
                    t.table_name as name,
                    CASE
                        WHEN t.table_type = 'BASE TABLE' THEN 'table'
                        ELSE 'view'
                    END as type,
                    d.description as comment
                FROM information_schema.tables t
                LEFT JOIN pg_class c ON c.relname = t.table_name
                LEFT JOIN pg_namespace n ON n.oid = c.relnamespace AND n.nspname = t.table_schema
                LEFT JOIN pg_description d ON d.objoid = c.oid AND d.objsubid = 0
                WHERE t.table_schema = $1
                ORDER BY t.table_name
            `, [schema]);

            return result.rows;
        } catch (error) {
            console.error(`获取 schema ${schema} 的 tables 失败`, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }

    async getColumns(
        client: Pool,
        _database: string,
        table: string,
        schema: string = 'public'
    ): Promise<{
        name: string,
        type: string,
        isPrimaryKey: boolean,
        isForeignKey: boolean,
        references?: { table: string, column: string },
        comment?: string
    }[]> {
        try {
            // Get column information with comments
            const columnsResult = await client.query(`
                SELECT
                    c.column_name as name,
                    c.data_type as type,
                    c.character_maximum_length as max_length,
                    c.numeric_precision as precision,
                    c.numeric_scale as scale,
                    d.description as comment
                FROM information_schema.columns c
                LEFT JOIN pg_class pc ON pc.relname = c.table_name
                LEFT JOIN pg_namespace pn ON pn.oid = pc.relnamespace AND pn.nspname = c.table_schema
                LEFT JOIN pg_attribute pa ON pa.attrelid = pc.oid AND pa.attname = c.column_name
                LEFT JOIN pg_description d ON d.objoid = pc.oid AND d.objsubid = pa.attnum
                WHERE c.table_schema = $1
                  AND c.table_name = $2
                ORDER BY c.ordinal_position
            `, [schema, table]);

            // Get primary key information
            const pkResult = await client.query(`
                SELECT
                    kcu.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                WHERE tc.constraint_type = 'PRIMARY KEY'
                    AND tc.table_schema = $1
                    AND tc.table_name = $2
            `, [schema, table]);

            // Create a set of primary key columns for quick lookup
            const primaryKeyColumns = new Set();
            pkResult.rows.forEach((row: any) => {
                primaryKeyColumns.add(row.column_name);
            });

            // Get foreign key information
            const fkResult = await client.query(`
                SELECT
                    kcu.column_name,
                    ccu.table_schema as foreign_table_schema,
                    ccu.table_name as foreign_table_name,
                    ccu.column_name as foreign_column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu
                    ON tc.constraint_name = kcu.constraint_name
                    AND tc.table_schema = kcu.table_schema
                JOIN information_schema.constraint_column_usage ccu
                    ON ccu.constraint_name = tc.constraint_name
                    AND ccu.table_schema = tc.table_schema
                WHERE tc.constraint_type = 'FOREIGN KEY'
                    AND tc.table_schema = $1
                    AND tc.table_name = $2
            `, [schema, table]);

            // Create a map of foreign key columns for quick lookup
            const foreignKeyMap = new Map();
            fkResult.rows.forEach((row: any) => {
                foreignKeyMap.set(row.column_name, {
                    table: row.foreign_table_name,
                    column: row.foreign_column_name
                });
            });

            // Format the column data
            return columnsResult.rows.map((column: any) => {
                // Format the type with length/precision/scale if applicable
                let fullType = column.type;
                if (column.max_length) {
                    fullType += `(${column.max_length})`;
                } else if (column.precision && column.scale) {
                    fullType += `(${column.precision},${column.scale})`;
                }

                const isPrimaryKey = primaryKeyColumns.has(column.name);
                const isForeignKey = foreignKeyMap.has(column.name);

                return {
                    name: column.name,
                    type: fullType,
                    isPrimaryKey,
                    isForeignKey,
                    references: isForeignKey ? foreignKeyMap.get(column.name) : undefined,
                    comment: column.comment
                };
            });
        } catch (error) {
            console.error(`Error getting columns for table ${schema}.${table}`, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }

    async executeQuery(
        client: Pool,
        query: string,
        _database?: string,
        schema?: string
    ): Promise<{
        columns: string[],
        rows: any[],
        rowCount: number
    }> {
        try {
            if (schema) {
                await client.query(`SET search_path TO ${schema}`);
            }

            console.debug(`执行查询语句 : ${query}`);
            const result = await client.query(query);

            const columns = result.fields ? result.fields.map((field: any) => field.name) : [];

            return {
                columns,
                rows: result.rows,
                rowCount: (result.rows ?? []).length
            };
        } catch (error) {
            console.error(`执行查询语句失败: ${query}`, error instanceof Error ? error : new Error(String(error)));
            throw error;
        }
    }
}
