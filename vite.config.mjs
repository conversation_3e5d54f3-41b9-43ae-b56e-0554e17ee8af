import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";
import tailwindcss from "@tailwindcss/vite";
import fs from "fs";


const entryConfigs = [
    {
        name: 'workflow',
        sourceFile: resolve(__dirname, "webview-ui/workflow/index.html"),
        distFile: resolve(__dirname, "dist/webview-ui/workflow/index.html"),
    },
    {
        name: 'er-diagram',
        sourceFile: resolve(__dirname, "webview-ui/er-diagram/index.html"),
        distFile: resolve(__dirname, "dist/webview-ui/er-diagram/index.html")
    },
    {
        name: 'user-profile',
        sourceFile: resolve(__dirname, "webview-ui/user-profile/index.html"),
        distFile: resolve(__dirname, "dist/webview-ui/user-profile/index.html")
    },
    {
        name: 'chat',
        sourceFile: resolve(__dirname, "webview-ui/chat/index.html"),
        distFile: resolve(__dirname, "dist/webview-ui/chat/index.html")
    }
]

// https://vite.dev/config/
export default defineConfig({
    plugins: [
        react(),
        tailwindcss(),
        {
            name: "move-html",
            closeBundle() {
                const sourceList =entryConfigs.map(item => item.distFile);

                for (let i = 0; i < sourceList.length; i++) {
                    // 构建结束后移动 index.html
                    const src = sourceList[i];
                    const dest = src.replace("/webview-ui", "");
                    const destDir = dest.replace("/index.html", "");
                    if (fs.existsSync(src)) {
                        if (!fs.existsSync(destDir)) {
                            fs.mkdirSync(destDir, { recursive: true });
                        }
                        fs.renameSync(src, dest);
                    }

                    // 删除空目录
                    const emptyDir = src.replace("/index.html", "");
                    if (
                        fs.existsSync(emptyDir) &&
                        fs.readdirSync(emptyDir).length === 0
                    ) {
                        fs.rmdirSync(emptyDir);
                    }
                }

                const parentDir = "dist/webview-ui";
                if (
                    fs.existsSync(parentDir) &&
                    fs.readdirSync(parentDir).length === 0
                ) {
                    fs.rmdirSync(parentDir);
                }
            },
        },
    ],
    build: {
        outDir: "dist",
        target: "esnext",
        rollupOptions: {
            input: entryConfigs.reduce((acc, item) => {
                acc[item.name] = item.sourceFile;
                return acc; 
            }, {}),
            output: {
                entryFileNames: `[name]/[name].js`,
                chunkFileNames: `[name]/[name].js`,
                assetFileNames: `[name]/[name].[ext]`,
                format: "es",
            },
        },
    },
});
