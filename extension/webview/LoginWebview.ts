import * as vscode from 'vscode'

export class LoginWebviewContainer {
    private _panel: vscode.WebviewPanel | undefined
    private _context: vscode.ExtensionContext

    constructor(context: vscode.ExtensionContext) {
        this._context = context
    }

    public openLoginWebviewPanel() {
        if (this._panel) {
            this._panel.reveal()
            return
        }

        this._panel = vscode.window.createWebviewPanel(
            'loginWebview',
            '登录',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
            },
        )

        this._panel.webview.html = this._getWebviewContent()

        this._panel.onDidDispose(
            () => {
                this._panel = undefined
            },
            null,
            [],
        )

        this._panel.webview.onDidReceiveMessage(
            async message => {
                if (message.command === 'openExternal') {
                    try {
                        await vscode.env.openExternal(vscode.Uri.parse(message.url))
                    } catch (error) {
                        vscode.window.showErrorMessage('打开外部链接失败')
                    }
                }
            },
            undefined,
            [],
        )
    }

    private _getWebviewContent() {
        return `<!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>登录</title>
        </head>
        <body>
            <div class="login-container">
                <button onclick="login()">登录</button>
            </div>
            <script>
                function login() {
                    const vscode = acquireVsCodeApi();
                    vscode.postMessage({
                        command: 'openExternal',
                        url: 'https://nerd-dev.lamdar.cn/login'
                    });
                }
            </script>
        </body>
        </html>`
    }
}