import { Node } from 'pocketflow'
import { grepSearch } from '../utils/grepSearch'
import {
    CodeAgentActionEnum,
    CodingAgentSharedStore,
    History,
    Output,
    SearchResult,
} from '../types'

interface PrepResult {
    query: string
    caseSensitive: boolean
    includePattern?: string
    excludePattern?: string
    workingDir: string
    history: History[]
    output: Output
}

export class GrepSearchActionNode extends Node<CodingAgentSharedStore> {
    async prep(shared: CodingAgentSharedStore): Promise<PrepResult> {
        await shared.output.text('GrepSearchAction - prep: 获取搜索参数')

        const params = shared.currentAction!.params

        const searchParams = {
            query: params.query,
            caseSensitive: params.caseSensitive ?? true,
            includePattern: params.includePattern,
            excludePattern: params.excludePattern,
            workingDir: params.workingDir,
            history: shared.history,
            output: shared.output,
        }

        return searchParams
    }

    async exec(prepResult: PrepResult): Promise<{ matches: SearchResult[]; success: boolean }> {
        prepResult.output.text(`正在执行搜索: ${prepResult.query}`)

        const [success, matches] = await grepSearch(
            prepResult.query,
            prepResult.caseSensitive,
            prepResult.includePattern,
            prepResult.excludePattern,
            prepResult.workingDir,
        )

        prepResult.output.text(
            `GrepSearchAction - exec: 搜索${success ? '成功' : '失败'}，找到 ${matches.length} 个匹配`,
        )

        return { success, matches }
    }

    async post(
        shared: CodingAgentSharedStore,
        prepResult: PrepResult,
        execResult: { matches: SearchResult[]; success: boolean },
    ) {
        shared.history.push({
            role: 'user',
            content: JSON.stringify({
                ...shared.currentAction,
                result: execResult.matches,
                success: execResult.success,
            }),
        })
        shared.currentAction = null

        return CodeAgentActionEnum.default
    }
}
