import React, { useState } from "react";
import { ChevronRightIcon, CheckIcon } from "@radix-ui/react-icons";
import { DropdownMenu } from "radix-ui";
import { BasicCascaderProps, RenderMenuItemsFn } from "./interface";
/**
 * 递归渲染菜单项和子菜单
 * @param {CascaderItem[]} items - 要渲染的菜单项数组
 * @param {string[]} currentPath - 到达此层级的当前值路径
 * @param {string[]} currentLabelPath - 到达此层级的当前标签路径
 * @param {Function} onSelect - 终端项目被选中时的回调函数
 * @param {string} selectedValuePath - 当前选定值路径的字符串表示
 */
const renderMenuItems: RenderMenuItemsFn = (items, currentPath, currentLabelPath, onSelect, selectedValuePath) => {
  return items.map((item) => {
    const itemPath = [...currentPath, item.value];
    const itemLabelPath = [...currentLabelPath, item.label]; // 构造当前项的标签路径
    const itemPathString = itemPath.join('/');

    if (item.children && item.children.length > 0) {
      return (
        <DropdownMenu.Sub key={item.value}>
          <DropdownMenu.SubTrigger
            className="flex items-center w-full text-sm leading-none text-gray-700 rounded-md px-2.5 py-2 relative select-none outline-none cursor-pointer
                       hover:bg-gray-100 focus:bg-gray-100
                       data-[highlighted]:bg-blue-500 data-[highlighted]:text-white data-[highlighted]:outline-none
                       data-[disabled]:text-gray-400 data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
            disabled={item.disabled}
          >
            <span className="flex-grow">{item.label}</span>
            <div className="ml-auto pl-4 text-gray-500 data-[highlighted]:text-white">
              <ChevronRightIcon className="w-4 h-4" />
            </div>
          </DropdownMenu.SubTrigger>
          <DropdownMenu.Portal>
            <DropdownMenu.SubContent
              className="min-w-[200px] bg-white rounded-md p-1 shadow-lg ring-1 ring-black ring-opacity-5
                         will-change-[opacity,transform]
                         data-[side=right]:animate-in data-[side=right]:slide-in-from-left-2
                         data-[side=left]:animate-in data-[side=left]:slide-in-from-right-2
                         data-[side=bottom]:animate-in data-[side=bottom]:slide-in-from-top-2
                         data-[side=top]:animate-in data-[side=top]:slide-in-from-bottom-2"
              sideOffset={5}
              alignOffset={-5}
            >
              {renderMenuItems(item.children, itemPath, itemLabelPath, onSelect, selectedValuePath)}
            </DropdownMenu.SubContent>
          </DropdownMenu.Portal>
        </DropdownMenu.Sub>
      );
    } else {
      return (
        <DropdownMenu.Item
          key={item.value}
          className="flex items-center w-full text-sm leading-none text-gray-700 rounded-md px-2.5 py-2 relative select-none outline-none cursor-pointer
                     hover:bg-gray-100 focus:bg-gray-100
                     data-[highlighted]:bg-blue-500 data-[highlighted]:text-white data-[highlighted]:outline-none
                     data-[disabled]:text-gray-400 data-[disabled]:pointer-events-none data-[disabled]:opacity-50"
          onSelect={() => onSelect(itemPath, item.label, itemLabelPath)} // 传递 itemLabelPath
          disabled={item.disabled}
        >
          <span className="flex-grow">{item.label}</span>
          {selectedValuePath === itemPathString && (
            <div className="ml-auto pl-4 text-blue-600 data-[highlighted]:text-white">
              <CheckIcon className="w-4 h-4" />
            </div>
          )}
        </DropdownMenu.Item>
      );
    }
  });
};

/**
 * BasicCascader 组件
 * @param {BasicCascaderProps} props - 组件的 props
 */
export const BasicCascader: React.FC<BasicCascaderProps> = ({ data, triggerLabel = "打开选择器", onChange, defaultSelectedPath = [] , defaultSelectedLabelPath = []}) => {
  const [selectedPath, setSelectedPath] = useState<string[]>(defaultSelectedPath);
  const [selectedLabelPath, setSelectedLabelPath] = useState<string[]>(defaultSelectedLabelPath); // 新增: 存储选定的标签路径
  // currentDisplayLabel 现在将由 selectedLabelPath 派生，或者使用 triggerLabel
  // const [currentDisplayLabel, setCurrentDisplayLabel] = useState<string>(triggerLabel); // 此状态不再直接设置

  /**
   * 处理终端项目的选择
   * @param {string[]} path - 选定项目的值路径
   * @param {string} label - 选定项目的标签
   * @param {string[]} labelPath - 选定项目的标签路径
   */
  const handleSelect = (path: string[], label: string, labelPath: string[]) => {
    setSelectedPath(path);
    setSelectedLabelPath(labelPath); // 设置选定的标签路径

    if (onChange) {
      onChange({ path, label, labelPath }); // 将 labelPath 传递给 onChange 回调
    }
  };

  // 确定要在触发按钮上显示的标签
  // 如果有选定的标签路径，则显示它，否则显示初始的 triggerLabel
  const displayLabel = selectedLabelPath.length > 0 ? selectedLabelPath.join(' / ') : triggerLabel;

  return (
    <DropdownMenu.Root onOpenChange={() => {
        // 可选: 如果希望在菜单关闭时重置/清除路径，可以在这里处理
        // 例如: if (!open && selectedPath.length === 0) setSelectedLabelPath([])
    }}>
      <DropdownMenu.Trigger asChild>
        <button
          className="inline-flex items-center justify-between rounded-md border border-gray-300 bg-white px-3.5 py-2 text-sm font-medium text-gray-700 shadow-sm
                     hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2
                     min-w-[200px] w-auto text-left group"
          aria-label="打开级联选择器 (Open cascader)"
        >
          <span className="truncate">{displayLabel}</span>
          <ChevronRightIcon
            className="ml-2 h-4 w-4 text-gray-400 transform transition-transform duration-200
                       group-data-[state=open]:rotate-90"
          />
        </button>
      </DropdownMenu.Trigger>

      <DropdownMenu.Portal>
        <DropdownMenu.Content
          className="min-w-[var(--radix-dropdown-menu-trigger-width)] md:min-w-[220px] bg-white rounded-md p-1 shadow-lg ring-1 ring-black ring-opacity-5
                     overflow-hidden will-change-[opacity,transform]
                     data-[state=open]:animate-in data-[state=open]:fade-in-0 data-[state=open]:zoom-in-95
                     data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95
                     data-[side=bottom]:slide-in-from-top-2 data-[side=top]:slide-in-from-bottom-2"
          sideOffset={5}
        >
          {/* 初始调用 renderMenuItems 时，currentLabelPath 也为空数组 */}
          {renderMenuItems(data, [], [], handleSelect, selectedPath.join('/'))}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
};